{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/__tests__/components/libraryheader.test.tsx", "./src/__tests__/components/viewmodes/platformfamilyview.test.tsx", "./src/__tests__/components/ui/game/xboxgametile.test.tsx", "./src/__tests__/components/ui/utils/platformfamilygroup.test.tsx", "./src/__tests__/config/test-config.ts", "./src/__tests__/hooks/useviewmodestate.test.ts", "./src/__tests__/integration/platformfamilysearchintegration.test.tsx", "./src/__tests__/lib/utils/platformfamilystatsupdate.test.ts", "./src/__tests__/lib/utils/platformfamilyutils.test.ts", "./src/__tests__/lib/utils/viewmodepreferencemigration.test.ts", "./src/__tests__/lib/utils/viewmodetransitions.test.ts", "./src/__tests__/utils/test-helpers.ts", "./src/components/layout/appwrapper.tsx", "./src/components/layout/errorboundary.tsx", "./src/components/layout/footer.tsx", "./src/components/layout/header.tsx", "./src/components/layout/layout.tsx", "./src/components/layout/protectedroute.tsx", "./src/components/layout/sidebar.tsx", "./src/components/ui/error-boundary.tsx", "./src/components/ui/index.ts", "./src/components/ui/analytics/airecommendationscard.tsx", "./src/components/ui/analytics/artworkgallery.tsx", "./src/components/ui/analytics/cachemanagement.tsx", "./src/components/ui/analytics/collectioninsightscard.tsx", "./src/components/ui/analytics/enhancedsearchcard.tsx", "./src/components/ui/analytics/pricecard.tsx", "./src/components/ui/analytics/pricechart.tsx", "./src/components/ui/analytics/searchinsightspanel.tsx", "./src/components/ui/analytics/searchperformancedashboard.tsx", "./src/components/ui/analytics/index.ts", "./src/components/ui/analytics/stats-bar.tsx", "./src/components/ui/base/searcherrordisplay.tsx", "./src/components/ui/base/alert.tsx", "./src/components/ui/base/avatar.tsx", "./src/components/ui/base/badge.tsx", "./src/components/ui/base/button.tsx", "./src/components/ui/base/card.tsx", "./src/components/ui/base/checkbox.tsx", "./src/components/ui/base/command.tsx", "./src/components/ui/base/dialog.tsx", "./src/components/ui/base/dropdown-menu.tsx", "./src/components/ui/base/form.tsx", "./src/components/ui/base/game-image.tsx", "./src/components/ui/base/index.ts", "./src/components/ui/base/input.tsx", "./src/components/ui/base/label.tsx", "./src/components/ui/base/navigation-menu.tsx", "./src/components/ui/base/popover.tsx", "./src/components/ui/base/progress.tsx", "./src/components/ui/base/scroll-area.tsx", "./src/components/ui/base/select.tsx", "./src/components/ui/base/separator.tsx", "./src/components/ui/base/sheet.tsx", "./src/components/ui/base/skeleton.tsx", "./src/components/ui/base/slider.tsx", "./src/components/ui/base/switch.tsx", "./src/components/ui/base/tabs.tsx", "./src/components/ui/base/textarea.tsx", "./src/components/ui/base/tooltip.tsx", "./src/components/ui/chat/chatinput.tsx", "./src/components/ui/chat/chatwindow.tsx", "./src/components/ui/chat/messagebubble.tsx", "./src/components/ui/chat/index.ts", "./src/components/ui/enhanced/enhanced-collection-analytics.tsx", "./src/components/ui/enhanced/enhanced-cover-flow.tsx", "./src/components/ui/enhanced/enhanced-export-import.tsx", "./src/components/ui/enhanced/enhanced-grid-view.tsx", "./src/components/ui/enhanced/enhanced-library-navigator.tsx", "./src/components/ui/enhanced/enhanced-list-view.tsx", "./src/components/ui/enhanced/enhanced-smart-collections.tsx", "./src/components/ui/enhanced/enhanced-smart-filters.tsx", "./src/components/ui/enhanced/enhanced-stats-dashboard.tsx", "./src/components/ui/enhanced/enhanced-test.tsx", "./src/components/ui/enhanced/enhanced-timeline-view.tsx", "./src/components/ui/enhanced/index.ts", "./src/components/ui/filters/developerfilter.tsx", "./src/components/ui/filters/filterchip.tsx", "./src/components/ui/filters/filterpanel.tsx", "./src/components/ui/filters/filterpresets.tsx", "./src/components/ui/filters/floatingfilterbutton.tsx", "./src/components/ui/filters/gamesearch.tsx", "./src/components/ui/filters/genrefilter.tsx", "./src/components/ui/filters/horizontalfilterbar.tsx", "./src/components/ui/filters/platformfilter.tsx", "./src/components/ui/filters/publisherfilter.tsx", "./src/components/ui/filters/ratingfilter.tsx", "./src/components/ui/filters/tagfilter.tsx", "./src/components/ui/filters/yearfilter.tsx", "./src/components/ui/filters/index.ts", "./src/components/ui/filters/search-suggestions.tsx", "./src/components/ui/filters/sort-options.tsx", "./src/components/ui/game/threedboxview.tsx", "./src/components/ui/game/xboxgametile.tsx", "./src/components/ui/game/game-card-skeleton.tsx", "./src/components/ui/game/game-card.tsx", "./src/components/ui/game/game-image.tsx", "./src/components/ui/game/game-modal.tsx", "./src/components/ui/game/game-results.tsx", "./src/components/ui/game/index.ts", "./src/components/ui/game/premium-game-card-demo.tsx", "./src/components/ui/game/premium-game-card.tsx", "./src/components/ui/game/star-rating.tsx", "./src/components/ui/game/status-badge.tsx", "./src/components/ui/game/unified-game-card.tsx", "./src/components/ui/game/virtualized-game-grid.tsx", "./src/components/ui/import/apikeymanager.tsx", "./src/components/ui/import/boxartsearch.tsx", "./src/components/ui/import/csvimportcard.tsx", "./src/components/ui/import/customartworkupload.tsx", "./src/components/ui/import/platformimportgrid.tsx", "./src/components/ui/import/platformkeyform.tsx", "./src/components/ui/import/steamidconfiguration.tsx", "./src/components/ui/import/steamimportcard.tsx", "./src/components/ui/import/index.ts", "./src/components/ui/library/bulk-operations-toolbar.tsx", "./src/components/ui/library/view-customization-controls.tsx", "./src/components/ui/library/virtualized-library-grid.tsx", "./src/components/ui/settings/artworkapikeymanager.tsx", "./src/components/ui/tags/bulktagoperations.tsx", "./src/components/ui/tags/smarttagsuggestions.tsx", "./src/components/ui/tags/taganalytics.tsx", "./src/components/ui/tags/tagdashboard.tsx", "./src/components/ui/tags/taginput.tsx", "./src/components/ui/tags/tagmanager.tsx", "./src/components/ui/tags/index.ts", "./src/components/ui/utils/platformfamilygroup.tsx", "./src/components/ui/utils/platformgroup.tsx", "./src/components/ui/utils/image-gallery.tsx", "./src/components/ui/utils/index.ts", "./src/components/ui/utils/loading-spinner.tsx", "./src/contexts/authcontext.tsx", "./src/contexts/librarycontext.tsx", "./src/hooks/useairecommendations.ts", "./src/hooks/useapikeys.ts", "./src/hooks/usecsvimport.ts", "./src/hooks/usecollectioninsights.ts", "./src/hooks/usecustomartwork.ts", "./src/hooks/useenhancedai.ts", "./src/hooks/useenhancedgametags.ts", "./src/hooks/useenhancedlibrary.ts", "./src/hooks/useenhancedsearch.ts", "./src/hooks/usefilterpresets.ts", "./src/hooks/usefilters.ts", "./src/hooks/usegameactions.ts", "./src/hooks/usegametags.ts", "./src/hooks/useimagewithfallback.ts", "./src/hooks/usemovetolibrary.ts", "./src/hooks/usemultiplatformimport.ts", "./src/hooks/useprefetch.ts", "./src/hooks/usepricetracking.ts", "./src/hooks/usesearchanalytics.ts", "./src/hooks/usesearchcache.ts", "./src/hooks/usesearchfilters.ts", "./src/hooks/usesteamimport.ts", "./src/hooks/useusercollection.ts", "./src/hooks/useuserlibrary.ts", "./src/hooks/useuserpreferences.ts", "./src/hooks/useuserstats.ts", "./src/hooks/useusertags.ts", "./src/hooks/useviewmodestate.ts", "./src/hooks/filters/index.ts", "./src/hooks/filters/usefilteractions.ts", "./src/hooks/filters/usefilterpersistence.ts", "./src/hooks/filters/usefilterstate.ts", "./src/hooks/filters/usesearchfilterscomposed.ts", "./src/hooks/search/index.ts", "./src/hooks/search/useenhancedsearchcomposed.ts", "./src/hooks/search/usesearchactions.ts", "./src/hooks/search/usesearchanalytics.ts", "./src/hooks/search/usesearchhistory.ts", "./src/hooks/search/usesearchstate.ts", "./src/hooks/search/usesearchutils.ts", "./src/hooks/search/usevoicesearch.ts", "./src/lib/airecommendationservice.ts", "./src/lib/api.ts", "./src/lib/apikeystore.ts", "./src/lib/collectioninsightsservice.ts", "./src/lib/csvimportservice.ts", "./src/lib/customartworkservice.ts", "./src/lib/encryption.ts", "./src/lib/enhancedaiservice.ts", "./src/lib/enhancedsearchservice.ts", "./src/lib/errorlogger.ts", "./src/lib/filterutils.ts", "./src/lib/gamestatusutils.ts", "./src/lib/icons.ts", "./src/lib/libraryservice.ts", "./src/lib/performanceoptimizer.ts", "./src/lib/pricetrackingservice.ts", "./src/lib/sortutils.ts", "./src/lib/steamimportservice.ts", "./src/lib/supabase.ts", "./src/lib/userrecordutils.ts", "./src/lib/utils.ts", "./src/lib/api/collections.ts", "./src/lib/api/games.ts", "./src/lib/api/index.ts", "./src/lib/api/platformmappings.ts", "./src/lib/api/pricing.ts", "./src/lib/api/recommendations.ts", "./src/lib/api/steamgriddb.ts", "./src/lib/api/users.ts", "./src/lib/imports/baseplatformservice.ts", "./src/lib/imports/epicimportservice.ts", "./src/lib/imports/playstationimportservice.ts", "./src/lib/imports/steamplatformadapter.ts", "./src/lib/imports/xboximportservice.ts", "./src/lib/services/artworkapirouter.ts", "./src/lib/services/artworksearchservice.ts", "./src/lib/services/collectionservice.ts", "./src/lib/services/datatransformationservice.ts", "./src/lib/services/gameservice.ts", "./src/lib/services/index.ts", "./src/lib/services/orchestrationservice.ts", "./src/lib/services/ratelimitmanager.ts", "./src/lib/services/searchcacheservice.ts", "./src/lib/services/userservice.ts", "./src/lib/supabase/index.ts", "./src/lib/supabase/supabase-auth.ts", "./src/lib/supabase/supabase-client.ts", "./src/lib/supabase/supabase-database.ts", "./src/lib/supabase/supabase-search-analytics.ts", "./src/lib/supabase/supabase-storage.ts", "./src/lib/supabase/supabase-tags.ts", "./src/lib/supabase/supabase-utils.ts", "./src/lib/supabase/__mocks__/supabase-client.ts", "./src/lib/utils/arrayutils.ts", "./src/lib/utils/baseutils.ts", "./src/lib/utils/dateutils.ts", "./src/lib/utils/encryptionutils.ts", "./src/lib/utils/errorutils.ts", "./src/lib/utils/filterutils.ts", "./src/lib/utils/gamededuplicationutils.ts", "./src/lib/utils/gameutils.ts", "./src/lib/utils/globalimagehandler.ts", "./src/lib/utils/igdbimageutils.ts", "./src/lib/utils/imageutils.ts", "./src/lib/utils/index.ts", "./src/lib/utils/performanceutils.ts", "./src/lib/utils/platformfamilyutils.ts", "./src/lib/utils/platformmapper.ts", "./src/lib/utils/searcherrorhandler.ts", "./src/lib/utils/searchscoringutils.ts", "./src/lib/utils/searchutils.ts", "./src/lib/utils/smartdebounce.ts", "./src/lib/utils/stringutils.ts", "./src/lib/utils/validationutils.ts", "./src/lib/utils/viewmodepreferencemigration.ts", "./src/lib/utils/viewmodetransitions.ts", "./src/lib/utils/__tests__/imageutils.test.ts", "./src/pages/aiagent.tsx", "./src/pages/deals.tsx", "./src/pages/gamedetail.tsx", "./src/pages/login.tsx", "./src/pages/profile.tsx", "./src/pages/settings.tsx", "./src/pages/signup.tsx", "./src/pages/dashboard/index.tsx", "./src/pages/dashboard/types.ts", "./src/pages/dashboard/components/aifeaturessection.tsx", "./src/pages/dashboard/components/aiintelligencesection.tsx", "./src/pages/dashboard/components/dashboardheader.tsx", "./src/pages/dashboard/components/hotdealssection.tsx", "./src/pages/dashboard/components/recentactivitysection.tsx", "./src/pages/dashboard/components/statsoverview.tsx", "./src/pages/dashboard/hooks/usedashboarddata.ts", "./src/pages/dashboard/hooks/usedashboardstats.ts", "./src/pages/library/index.tsx", "./src/pages/library/types.ts", "./src/pages/library/components/libraryheader.tsx", "./src/pages/library/components/librarystats.tsx", "./src/pages/library/components/searchcontrols.tsx", "./src/pages/library/components/librarytabs/analyticstab.tsx", "./src/pages/library/components/librarytabs/artworktab.tsx", "./src/pages/library/components/librarytabs/collectionstab.tsx", "./src/pages/library/components/librarytabs/exporttab.tsx", "./src/pages/library/components/librarytabs/filterstab.tsx", "./src/pages/library/components/librarytabs/librarytab.tsx", "./src/pages/library/components/librarytabs/statstab.tsx", "./src/pages/library/components/librarytabs/index.tsx", "./src/pages/library/components/viewmodes/platformfamilyview.tsx", "./src/pages/library/components/viewmodes/platformview.tsx", "./src/pages/library/components/viewmodes/statusview.tsx", "./src/pages/library/components/viewmodes/index.tsx", "./src/pages/library/hooks/index.ts", "./src/pages/library/hooks/uselibraryactions.ts", "./src/pages/library/hooks/uselibrarydata.ts", "./src/pages/library/hooks/uselibraryfilters.ts", "./src/pages/search/index.tsx", "./src/pages/search/components/searchfilters.tsx", "./src/pages/search/components/searchheader.tsx", "./src/pages/search/components/searchinterface.tsx", "./src/pages/search/components/searchresults.tsx", "./src/pages/search/components/index.ts", "./src/pages/search/hooks/index.ts", "./src/pages/search/hooks/usesearchdata.ts", "./src/pages/search/hooks/usesearchfilters.ts", "./src/pages/wishlist/index.tsx", "./src/pages/wishlist/types.ts", "./src/pages/wishlist/components/wishlistcontent.tsx", "./src/pages/wishlist/components/wishlistheader.tsx", "./src/pages/wishlist/components/wishliststats.tsx", "./src/pages/wishlist/hooks/index.ts", "./src/pages/wishlist/hooks/usewishlistactions.ts", "./src/pages/wishlist/hooks/usewishlistdata.ts", "./src/pages/wishlist/hooks/usewishlistfilters.ts", "./src/test/setup.ts", "./src/types/database.ts", "./src/types/filters.ts", "./src/types/index.ts", "./src/types/library.ts"], "errors": true, "version": "5.6.3"}