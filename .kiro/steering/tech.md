# Technology Stack & Build System

## Core Technologies

### Frontend Framework
- **React 18+** with TypeScript for type-safe development
- **Vite** as build tool for fast development and optimized production builds
- **React Router v7** for client-side routing with lazy loading
- **TanStack Query** (React Query) for server state management and caching

### UI & Styling
- **Tailwind CSS v3+** with custom configuration and design tokens
- **shadcn/ui** component library for consistent design system
- **Lucide React** for iconography
- **CSS custom properties** for theming (light/dark mode support)

### Backend & Database
- **Supabase** as primary backend service
  - PostgreSQL database with Row Level Security (RLS)
  - Authentication with JWT tokens
  - Storage for file uploads
  - Edge Functions for API proxying
- **Real-time subscriptions** for live data updates

### External APIs
- **IGDB** (primary game database)
- **TheGamesDB** (additional game metadata)
- **Steam Web API** (library import)
- **SerpAPI** (web search for AI features)
- **OpenAI, DeepSeek, Google Gemini** (AI providers)

### Development Tools
- **TypeScript** with strict mode enabled
- **ESLint** with React and TypeScript rules
- **Vitest** with React Testing Library for testing
- **React Hook Form + Zod** for form validation

## Build Commands

### Development
```bash
npm run dev          # Start development server (Vite)
npm run build        # Build for production (TypeScript + Vite)
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm test             # Run tests with Vitest
npm run test:run     # Run tests once (CI mode)
```

### Database Management
```bash
npm run clean-db     # Clean database using TypeScript script
npm run clean-db-cli # Clean database using shell script
```

### Supabase Commands
```bash
supabase start       # Start local Supabase
supabase stop        # Stop local Supabase
supabase db push     # Push migrations to remote
supabase db pull     # Pull schema from remote
supabase gen types   # Generate TypeScript types
```

## Configuration Files

### Build Configuration
- **vite.config.ts**: Vite configuration with path aliases (`@/` → `./src/`)
- **tsconfig.json**: TypeScript configuration with project references
- **tailwind.config.js**: Tailwind CSS with custom theme and animations
- **eslint.config.js**: ESLint configuration with React and TypeScript rules

### Environment Variables
Required variables in `.env`:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_IGDB_CLIENT_ID=your_igdb_client_id
VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
```

Optional API keys for enhanced features:
```env
VITE_OPENAI_API_KEY=your_openai_api_key
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key
VITE_GEMINI_API_KEY=your_gemini_api_key
VITE_SERPAPI_KEY=your_serpapi_key
VITE_STEAM_API_KEY=your_steam_api_key
VITE_THEGAMESDB_API_KEY=your_thegamesdb_api_key
```

## Performance Optimizations

### Code Splitting
- Lazy loading for all page components
- Dynamic imports for heavy libraries
- Route-based code splitting

### Caching Strategy
- TanStack Query with 10-minute stale time
- Optimistic updates for better UX
- Intelligent retry logic for failed requests

### UI Performance
- Virtualized lists for large datasets (react-window)
- Image lazy loading and optimization
- Debounced search inputs
- Loading skeletons and suspense boundaries

## Development Standards

### Code Quality
- TypeScript strict mode enforced
- ESLint rules for React hooks and TypeScript
- Consistent import organization
- Path aliases for clean imports (`@/components`, `@/lib`, etc.)

### Testing Strategy
- Vitest for unit and integration tests
- React Testing Library for component testing
- Mock external API calls in tests
- Test coverage for critical business logic

### Security Practices
- API keys encrypted with AES-256 before storage
- Row Level Security (RLS) on all database tables
- Input validation with Zod schemas
- HTTPS-only communication
- No sensitive data in client-side code