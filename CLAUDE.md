# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 MANDATORY DEVELOPMENT RULES

### Core Workflow Requirements

- **ALWAYS push to GitHub** when a task is completed
- **ALWAYS run `npm run lint`** and fix all TypeScript issues before pushing
- **ALWAYS check database schema** before making database-related changes
- **ALWAYS use Supabase migrations** for database schema modifications

### UI/UX Requirements

- **ALWAYS create beautiful, modern UI/UX designs** following current design trends
- **ALWAYS use shadcn/ui components** for consistent design system
- **ALWAYS use Roboto font** from Google Fonts for typography
- **ALWAYS use Lucide React icons** for consistent iconography
- **ALWAYS ensure responsive design** for all screen sizes
- **ALWAYS implement proper loading states** and error handling

### Code Quality Requirements

- **ALWAYS keep codebase clean and optimized**
- **ALWAYS follow TypeScript strict mode** rules
- **ALWAYS use ESLint** and fix all warnings/errors
- **ALWAYS use proper TypeScript interfaces** and types
- **ALWAYS write self-documenting code** with clear variable names
- **ALWAYS use React Query** for server state management
- **ALWAYS use React Hook Form + Zod** for form validation

### Database Requirements

- **ALWAYS check existing database tables** before creating new ones
- **ALWAYS use Supabase helpers** from `src/lib/supabase.ts`
- **ALWAYS implement proper error handling** for database operations
- **ALWAYS use Row Level Security (RLS)** for data protection
- **ALWAYS use migrations** for schema changes (create in `supabase/migrations/` with timestamp format)
- **ALWAYS use MCP tools** when available for database operations and IDE integration

#### Migration Architecture (Updated July 2025)

- **CLEANED MIGRATION HISTORY**: Consolidated 16 duplicate migrations into 13 focused ones
- **CONSOLIDATED SCHEMA**: Single comprehensive migration replaces overlapping files
- **NO DUPLICATES**: Eliminated all table/index/policy duplications and conflicts
- **PERFORMANCE OPTIMIZED**: Advanced indexes and materialized views for query optimization
- **CURRENT MIGRATIONS** (13 total):
  1. `20250701000000_consolidated_database_schema.sql` - Complete base schema
  2. `20250702000000_simplify_ai_recommendations.sql` - AI recommendations table
  3. `20250703000000_add_critical_performance_indexes.sql` - Advanced performance indexes
  4. `20250704000000_create_user_collection_materialized_view.sql` - Materialized views
  5. `20250705000000_optimize_rls_policies.sql` - Optimized RLS policies
  6. `20250714000000_add_steam_import_fields.sql` - Steam import fields
  7. `20250714120000_create_secure_api_key_storage.sql` - Secure API key storage
  8. `20250714130000_fix_user_api_keys_rls.sql` - Fix API key RLS
  9. `20250714140000_comprehensive_rls_fix.sql` - Comprehensive RLS fix
  10. `20250715000000_clean_up_unused_api_keys.sql` - Clean up non-Steam API keys
  11. `20250715140000_add_custom_artwork_table.sql` - Custom artwork support
  12. `20250715150000_add_set_primary_artwork_rpc.sql` - Artwork management functions
  13. `20250719000000_create_missing_advanced_features_tables.sql` - Advanced features (tags, filter presets, recommendations)
- **BACKUP AVAILABLE**: Original migrations backed up in `supabase/migrations_backup/`

#### Supabase Migration Troubleshooting

**⚠️ Common Issue**: Migration history mismatches between local and remote database

**Symptoms**:
- `supabase db push` fails with "Remote migration versions not found"
- `supabase db pull` reports migration history mismatch

**Resolution Steps**:

1. **For Migration History Repair** (when remote has migrations not in local):
   ```bash
   # Mark specific migrations as reverted (use versions from error message)
   supabase migration repair --status reverted 20250714095139 20250714095158 [...]
   
   # Mark applied migrations as applied
   supabase migration repair --status applied 20250714130000 20250714140000 [...]
   ```

2. **For Fresh Sync** (when local is behind remote):
   ```bash
   # Pull latest schema from remote
   supabase db pull
   
   # This creates new migration files based on remote state
   ```

3. **For Complete Reset** (last resort):
   ```bash
   # Reset local Supabase environment
   supabase db reset
   
   # Re-run all migrations
   supabase db push
   ```

**Prevention Tips**:
- Always run `supabase db pull` after team members push migrations
- Use `supabase db diff` to preview changes before pushing
- Keep local repository up-to-date with `git pull` before database operations

### MCP Tools Integration

- **ALWAYS use `mcp__ide__getDiagnostics`** to check for TypeScript and linting errors
- **ALWAYS use `mcp__ide__executeCode`** for running code in Jupyter kernels when applicable
- **PREFER MCP tools** over manual CLI commands when MCP tools are available for the task
- **USE MCP tools** for enhanced IDE integration and better error reporting

#### MCP Configuration & Setup

**Supabase MCP Server** (`kfzwgkzvlbyxotnbhgqk`):

1. **Access Token Setup** (Required for database operations):
   ```bash
   # Set environment variable for MCP access
   export SUPABASE_ACCESS_TOKEN=your_supabase_access_token
   
   # Or add to your shell profile (.zshrc, .bashrc)
   echo 'export SUPABASE_ACCESS_TOKEN=your_access_token' >> ~/.zshrc
   ```

2. **Get Supabase Access Token**:
   - Visit [Supabase Dashboard](https://supabase.com/dashboard)
   - Go to Settings → API → Project API keys
   - Copy the `service_role` key (not the anon key)
   - Or create a personal access token in Account Settings

3. **MCP Tools Available**:
   - `mcp__supabase__list_migrations` - List database migrations
   - `mcp__supabase__execute_sql` - Run SQL queries
   - `mcp__supabase__apply_migration` - Apply new migrations
   - `mcp__supabase__list_tables` - View database schema
   - `mcp__supabase__get_logs` - Debug database issues

4. **Troubleshooting MCP Connection**:
   ```bash
   # If MCP tools fail with "Unauthorized"
   # Check access token is set
   echo $SUPABASE_ACCESS_TOKEN
   
   # Verify token has correct permissions
   # Should start with 'sb-' and be ~40 characters long
   ```

**ShadCN MCP Server**:
- `mcp__shadcn-ui__list_components` - Browse available UI components
- `mcp__shadcn-ui__get_component` - Get component source code
- `mcp__shadcn-ui__get_component_demo` - Get usage examples

### Component Requirements

- **ALWAYS use shadcn/ui components** as base components
- **ALWAYS create reusable custom components** in `src/components/ui/`
- **ALWAYS use Tailwind CSS** for styling with CSS variables
- **ALWAYS follow the established component structure**

## Project Overview

This is a personal game library web application built with React, TypeScript, and Vite. It allows users to search for games using multiple APIs (IGDB, RAWG, YouTube), manage their personal collection, and track gaming statistics through a modern web interface.

## Development Commands

### Core Application Commands

```bash
# Start development server
npm run dev

# Build for production (TypeScript compilation + Vite build)
npm run build

# Run linter (MANDATORY before pushing)
npm run lint

# Run tests (development and CI)
npm test            # Watch mode for development
npm run test:run    # Single run for CI

# Preview production build
npm run preview

# Stop development server (utility script)
./scripts/stop-npm-dev.sh
```

### Supabase Database Commands

```bash
# Database Operations
supabase db push           # Apply local migrations to remote database
supabase db pull           # Pull remote schema changes to local
supabase db diff           # Show differences between local and remote
supabase db reset          # Reset local database (destructive)

# Migration Management
supabase migration new <name>                    # Create new migration file
supabase migration repair --status applied <id>  # Mark migration as applied
supabase migration repair --status reverted <id> # Mark migration as reverted
supabase migration list                          # List all migrations

# Project Management
supabase link --project-ref <ref>               # Link to Supabase project
supabase status                                 # Show project status
supabase start                                  # Start local Supabase (optional)
supabase stop                                   # Stop local Supabase

# Troubleshooting
supabase db branches list                       # List database branches
supabase logs                                   # View project logs
```

### Database Sync Workflow

```bash
# Before starting work (daily routine)
git pull                    # Get latest code changes
supabase db pull           # Get latest database schema

# After making database changes
supabase db diff           # Preview changes
supabase db push           # Apply changes to remote

# If migration issues occur
supabase migration repair --status reverted <failing_migration_ids>
supabase migration repair --status applied <working_migration_ids>
supabase db push           # Retry push after repair
```

**Note**: This project uses Vitest for testing. Use `npm test` to run tests in watch mode or `npm run test:run` for a single test run.

## Tech Stack & Dependencies

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **UI Components**: **shadcn/ui components** (MANDATORY - always use these)
- **Typography**: **Roboto font** from Google Fonts (MANDATORY)
- **Icons**: **Lucide React** (MANDATORY - always use these)
- **Database**: Supabase (PostgreSQL with Auth and Storage)
- **External APIs**: IGDB, RAWG, TheGamesDB, YouTube Data API (via Supabase Edge Functions)
- **AI Integration**: Multiple AI providers (OpenAI, DeepSeek, Gemini) for conversational assistance and recommendations
- **Artwork Sources**: TheGamesDB and SteamGridDB for high-quality game artwork
- **Forms**: React Hook Form with Zod validation (MANDATORY)
- **State Management**: React Query for server state, React Context for auth (MANDATORY)
- **Toast Notifications**: React Hot Toast
- **Routing**: React Router v7
- **Quality Tools**: ESLint, TypeScript strict mode (MANDATORY)
- **Testing**: Vitest with React Testing Library

## Architecture

### Application Structure

The application follows a page-based routing structure with protected routes:

- **Public Routes**: `/login`, `/signup`
- **Protected Routes**: `/` (Dashboard), `/search`, `/library`, `/wishlist`, `/deals`, `/ai-agent`, `/settings`, `/profile`

All protected routes are wrapped with authentication checks and use a consistent layout with header, sidebar, and footer.

### Core Components Structure

- **`src/App.tsx`**: Main application component with routing configuration
- **`src/components/layout/`**: Layout components (Header, Sidebar, Layout wrapper, ErrorBoundary, ProtectedRoute)
- **`src/components/ui/`**: Reusable UI components organized by category:
  - **`base/`**: Core shadcn/ui components (button, card, dialog, etc.)
  - **`game/`**: Game-specific components (GameCard, GameModal, GameResults)
  - **`filters/`**: Search and filtering components (FilterPanel, SearchSuggestions)
  - **`enhanced/`**: Advanced library views (GridView, ListView, CoverFlow)
  - **`analytics/`**: Dashboard and analytics components
  - **`import/`**: Platform import and API key management
  - **`chat/`**: AI chat interface components
  - **`utils/`**: Utility components (LoadingSpinner, ImageGallery)
- **`src/pages/`**: Page components with modular sub-components:
  - **`Dashboard/`**: Main dashboard with components and hooks
  - **`Library/`**: Game library with tabs and view modes
  - **`Search/`**: Search interface with filters and results
  - **`Wishlist/`**: Wishlist management with stats
- **`src/contexts/AuthContext.tsx`**: Authentication context provider
- **`src/hooks/`**: Custom React hooks for business logic (23+ specialized hooks)
- **`src/lib/`**: Core services and utilities organized by functionality:
  - **`api/`**: API service classes for external integrations
  - **`services/`**: Business logic services (game, collection, user management)
  - **`supabase/`**: Database clients and utilities
  - **`utils/`**: Helper functions organized by category

### API Integration Architecture

- **`src/lib/api.ts`**: Main API service class (`APIService`) handling all external APIs
- **`src/lib/supabase.ts`**: Supabase client with organized helper functions
- **API Service Features**:
  - IGDB authentication via Twitch OAuth with token caching
  - Multi-API search (IGDB + RAWG + TheGamesDB) with result deduplication
  - YouTube video search integration
  - Retry mechanisms and comprehensive error handling
  - Game data normalization across different API formats

### Database Architecture

The application uses Supabase with these key database helpers:

- **`db.games`**: Game catalog management
- **`db.userGames`**: User's personal game collection
- **`db.priceTracking`**: Price tracking functionality
- **`db.userProfiles`**: User profile information
- **`db.userPreferences`**: User settings and preferences
- **`db.stats`**: Collection statistics

### Type System

- **`src/types/index.ts`**: Core TypeScript interfaces including `Game`, `IGDBGame`, `Platform`, `Genre`
- **`src/types/database.ts`**: Supabase-specific database types and schema definitions
- **`src/types/filters.ts`**: Advanced filtering type definitions for search and collection management
- **Comprehensive typing**: All API responses, user interactions, and database operations are strongly typed

## Configuration

### Environment Variables

Required environment variables (see `.env.example`):

```bash
# Supabase
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# API Keys
VITE_IGDB_CLIENT_ID=your_igdb_client_id
VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
VITE_THEGAMESDB_API_KEY=your_thegamesdb_api_key
VITE_YOUTUBE_API_KEY=your_youtube_api_key
VITE_STEAMGRIDDB_API_KEY=your_steamgriddb_api_key

# AI Service Keys (optional)
VITE_OPENAI_API_KEY=your_openai_api_key
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key
VITE_GEMINI_API_KEY=your_gemini_api_key
```

### Key Configuration Files

- **`vite.config.ts`**: Vite configuration with path aliases (`@/*` → `./src/*`) and performance optimizations
- **`vitest.config.ts`**: Vitest testing configuration with jsdom environment and path aliases
- **`tailwind.config.js`**: Custom CSS variables, extended color palette, custom animations (shimmer, float, glow, stagger-fade)
- **`tsconfig.json`**: TypeScript configuration with path mapping and strict mode
- **`.mcp.json`**: MCP server configuration with shadcn-ui and Supabase MCP servers
  - **Supabase MCP**: Project ref `kfzwgkzvlbyxotnbhgqk` for database operations
  - **ShadCN MCP**: For UI component scaffolding and updates
- **`main.tsx`**: React Query configuration with optimized retry logic and cache settings
- **`.github/workflows/claude.yml`**: GitHub Actions workflow for Claude Code integration

## Key Features

1. **Multi-API Game Search**: Searches IGDB, RAWG, TheGamesDB, and YouTube simultaneously for comprehensive results
2. **Game Collection Management**: Add games to library, wishlist, and track play status with advanced filtering
3. **Steam Library Import**: Complete Steam Web API integration with automatic library synchronization
4. **Advanced Search & Filtering**: Platform, genre, developer, publisher, rating filtering with virtualized results
5. **AI Gaming Assistant**: Conversational AI with multiple provider support for recommendations and discussions
6. **Box Art Discovery**: Gaming-specific APIs (TheGamesDB, SteamGridDB) for high-quality original game artwork and covers
7. **Price Tracking**: Monitor game prices across different platforms with alerts
8. **Collection Analytics**: Advanced insights including completion rates, genre distribution, and playtime analytics
9. **CSV Import/Export**: Full CSV support for bulk game management
10. **User Authentication**: Supabase Auth integration with comprehensive profile management
11. **Statistics Dashboard**: Real-time collection statistics and gaming insights with visual charts
12. **Responsive Design**: Mobile-first responsive layout with performance optimizations

## Development Architecture Notes

### API Service Design

The `APIService` class (`src/lib/api.ts`) is the central hub for all external API interactions:

- **Token Management**: Handles IGDB OAuth token lifecycle with automatic refresh
- **Request Retries**: Implements exponential backoff for failed requests
- **Data Normalization**: Converts API responses to consistent internal format
- **Error Handling**: Comprehensive error handling with user-friendly messages

### Supabase Integration

The application uses Supabase with comprehensive backend services:

- **`supabase/functions/`**: Four Deno-based edge functions (`igdb-proxy`, `thegamesdb-proxy`, `serpapi-proxy`, `steam-proxy`)
- **Security**: API keys are handled server-side, not exposed to client
- **CORS**: Properly configured for cross-origin requests
- **Migrations**: Located in `supabase/migrations/` with timestamp-based naming (12 active migrations)
- **Edge Functions**: TypeScript-based functions for secure API proxying and Steam integration
- **Migration Backup**: Original migration history preserved in `supabase/migrations_backup/`

### Data Flow

1. User searches for games → `APIService.searchBothAPIs()`
2. Concurrent API calls to IGDB and RAWG
3. Results normalized and deduplicated
4. UI components receive consistent `Game` interface
5. User actions (add to library) → Supabase database operations

### Component Architecture

- **UI Components**: Located in `src/components/ui/`, built with Tailwind CSS
- **Layout Components**: Consistent header/sidebar/footer structure
- **Page Components**: Feature-specific components in `src/pages/`
- **Custom Hooks**: 15+ specialized hooks in `src/hooks/` for AI, imports, analytics, search, and collection management
- **Advanced Features**: Voice search, NLP intent parsing, AI-powered recommendations, price tracking, Steam integration
- **Service Layer**: Dedicated services in `src/services/` for complex business logic and external integrations

## Specialized Hooks and Services

### Key Custom Hooks (23+ Specialized Hooks)

**Collection Management Hooks**:

- **`useUserLibrary`**: Core user game collection management
- **`useUserCollection`**: Advanced collection operations and filtering
- **`useGameActions`**: Game add/remove/update operations
- **`useMoveToLibrary`**: Game collection status management and updates

**AI & Recommendation Hooks**:

- **`useAIRecommendations`**: AI-powered game recommendations based on user collection
- **`useEnhancedAI`**: Enhanced AI chat features with multiple provider support

**Import & Integration Hooks**:

- **`useSteamImport`**: Steam library import with API integration
- **`useCSVImport`**: CSV file import functionality for bulk game management
- **`useMultiPlatformImport`**: Coordinated multi-platform import system

**Analytics & Insights Hooks**:

- **`useCollectionInsights`**: Advanced collection analytics and insights
- **`useUserStats`**: User collection statistics and analytics
- **`usePriceTracking`**: Price monitoring and alert functionality

**Search & Filtering Hooks**:

- **`useEnhancedSearch`**: Advanced search capabilities with NLP and voice recognition
- **`useSearchFilters`**: Advanced search filtering system
- **`useFilters`**: General-purpose filtering utilities

**Performance & Utility Hooks**:

- **`usePrefetch`**: Performance optimization with intelligent prefetching
- **`useGameDetails`**: Game metadata and details management
- **`useImageGallery`**: Image gallery and artwork management

### Service Layer

- **`aiRecommendationService.ts`**: AI recommendation engine with multiple providers
- **`collectionInsightsService.ts`**: Collection analysis and insights generation
- **`csvImportService.ts`**: CSV parsing and bulk import functionality
- **`enhancedSearchService.ts`**: NLP-powered search with intent parsing and voice recognition
- **`priceTrackingService.ts`**: Price monitoring and historical tracking across multiple stores
- **`steamImportService.ts`**: Steam API integration and library import
- **`errorLogger.ts`**: Comprehensive error logging with Beacon API support
- **`performanceOptimizer.ts`**: Performance utilities and optimization helpers
- **`filterUtils.ts`** and **`sortUtils.ts`**: Reusable utility functions

### Service Layer Architecture

The application uses a comprehensive service layer pattern:

**Core Services (`src/lib/services/`)**:

- **`gameService.ts`**: Game management and metadata operations
- **`collectionService.ts`**: User collection management and statistics
- **`userService.ts`**: User profile and preferences management
- **`analyticsService.ts`**: Collection insights and analytics generation
- **`importService.ts`**: Multi-platform library import coordination

**Platform Import Services (`src/lib/imports/`)**:

- **`steamPlatformAdapter.ts`**: Steam library import integration
- **`basePlatformAdapter.ts`**: Base adapter for platform integrations
- **`multiPlatformImport.ts`**: Coordinated multi-platform import system

**Specialized Services**:

- **`aiRecommendationService.ts`**: AI recommendation engine with multiple providers
- **`csvImportService.ts`**: CSV parsing and bulk import functionality
- **`enhancedSearchService.ts`**: NLP-powered search with intent parsing
- **`priceTrackingService.ts`**: Price monitoring across multiple stores
- **`steamImportService.ts`**: Steam API integration and library import
- **`errorLogger.ts`**: Comprehensive error logging with Beacon API

### Service Layer Usage Patterns

- **Always use service classes** for complex business logic instead of putting it in components
- **Services are stateless** - use React hooks for state management
- **Error handling is built-in** - services return structured error objects
- **All services support TypeScript** - comprehensive type safety throughout
- **Service Pattern**: Each service handles one domain concern with clear interfaces

## Database Schema

The application includes these main tables:

- **`games`**: Game catalog with metadata
- **`user_games`**: User's personal collection with play status
- **`user_profiles`**: Extended user information
- **`user_preferences`**: User settings and preferences
- **`price_tracking`**: Game price history
- **`user_collection_stats`**: Aggregated statistics
- **`ai_conversations`**: AI chat conversations with message history
- **`user_api_keys`**: Secure encrypted API key storage (Steam only as of July 2025)
- **`import_history`**: Library import history tracking (Steam only as of July 2025)
- **`custom_artwork`**: User-uploaded custom game artwork and box art

### Database Management Best Practices

#### Migration Management

**Core Principles**:
- **Never modify existing migrations** - Always create new ones for schema changes
- **Use timestamp naming** - Format: `YYYYMMDDHHMMSS_descriptive_name.sql`
- **Test migrations locally** before pushing to production
- **Keep migrations atomic** - One logical change per migration
- **Write reversible migrations** when possible

**Migration Workflow**:
```bash
# 1. Create new migration
supabase migration new add_new_feature_table

# 2. Edit the generated file in supabase/migrations/
# 3. Test locally (if using local development)
supabase db reset && supabase db push

# 4. Push to remote when ready
supabase db push
```

**Migration History Sync**:
- **Daily sync**: Run `supabase db pull` before starting work
- **Team coordination**: Communicate database changes with team
- **Conflict resolution**: Use repair commands for history mismatches
- **Backup strategy**: Keep migrations in version control

#### Database Operations Security

**RLS (Row Level Security)**:
- **Enable RLS** on all user-data tables
- **Write comprehensive policies** for SELECT, INSERT, UPDATE, DELETE
- **Test policies thoroughly** with different user scenarios
- **Use helper functions** for complex policy logic

**Performance Considerations**:
- **Add indexes** for frequently queried columns
- **Use materialized views** for complex aggregations
- **Monitor query performance** with EXPLAIN ANALYZE
- **Optimize JOIN operations** with proper foreign keys

**Data Integrity**:
- **Use constraints** to enforce business rules
- **Add foreign keys** for referential integrity
- **Validate data** before insertion
- **Handle edge cases** in application logic

**API Key Management**: As of July 2025, the application only supports Steam API keys. All other platform API keys have been removed for security and simplicity.

## AI Integration Architecture

### Enhanced AI Service (`src/lib/enhancedAIService.ts`)

The application includes a comprehensive AI service with the following features:

#### Multi-Provider Support

- **OpenAI**: GPT-4 for conversational AI
- **DeepSeek**: Alternative AI provider for chat
- **Gemini**: Google's AI model integration
- **Auto-detection**: Automatically selects the best available provider based on API keys

#### Conversational Features

- **Chat Management**: Create, manage, and persist conversations
- **Context Awareness**: Maintains conversation context and user preferences
- **Game Discussions**: Specialized prompts for gaming topics
- **Box Art Search**: Integrated web search for game artwork

#### Box Art Discovery

- **Gaming API Integration**: Uses TheGamesDB and SteamGridDB for high-quality artwork
- **Quality Assessment**: Evaluates image quality and authenticity from gaming sources
- **Original Artwork Detection**: Focuses on official artwork from gaming databases
- **Community Sources**: Access to community-curated artwork collections

### AI Components Structure

- **`src/components/ui/chat/`**: Chat UI components (ChatWindow, MessageBubble, ChatInput)
- **`src/components/ui/BoxArtSearch.tsx`**: Standalone box art search component
- **`src/hooks/useEnhancedAI.ts`**: React hooks for AI functionality
- **`src/pages/AIAgent.tsx`**: Main AI assistant page

### Conversation Database

The `ai_conversations` table stores:

- Conversation metadata (title, timestamps)
- Complete message history in JSONB format
- User association and RLS policies
- Automatic timestamp updates

## Performance Optimizations

- **React Query Configuration**: Optimized with smart retry logic, 10min staleTime, 15min gcTime, excludes 404/auth errors from retries
- **Virtualization**: React Window for large game collections to handle thousands of items
- **Prefetching**: Intelligent prefetching of game details and images via `usePrefetch` hook
- **Loading Skeletons**: Sophisticated skeleton loading states for better perceived performance
- **Vite Optimizations**: Excludes lucide-react from pre-bundling for faster builds
- **Image Optimization**: Lazy loading and responsive images throughout the application
- **Route-Based Code Splitting**: Lazy loading for all protected pages
- **Exponential Backoff**: Smart retry mechanisms with 30-second max delay

## Common Development Patterns

- **Type Safety**: All API responses are strongly typed
- **Error Boundaries**: Consistent error handling across components
- **State Management**: React Query for server state, Context API for global state
- **Form Handling**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS with HSL color variables for theming
- **Authentication**: Protected routes with automatic redirect handling

## Error Handling Architecture

- **Enhanced Error Logging**: Structured error logging with context, timestamps, and user IDs via `src/lib/errorLogger.ts`
- **Beacon API**: Uses `navigator.sendBeacon` for reliable error reporting even on page unload
- **Multi-Provider Fallbacks**: AI services automatically fall back to alternative providers on errors
- **Retry Logic**: Exponential backoff for API calls with smart error classification
- **User-Friendly Messages**: Error boundaries provide graceful degradation with helpful messages

## Steam Integration Architecture (Completed)

### Steam Web API Integration

The Steam integration is now fully functional and provides complete library import capabilities:

#### Core Steam Service (`src/lib/steamImportService.ts`)

- **Profile Validation**: Fetches and validates Steam user profiles
- **Library Access**: Retrieves complete game library with playtime data
- **Game Details**: Fetches detailed game information from Steam Store API
- **Duplicate Detection**: Intelligent game matching using Steam App IDs and titles
- **Import Tracking**: Full import source tracking and last played timestamps

#### Steam Database Schema

```sql
-- Games table Steam fields
steam_app_id text UNIQUE,  -- Steam's unique application identifier
steam_url text,            -- Direct link to Steam store page

-- User games table tracking fields
import_source text,        -- Tracks import source (steam only)
last_played timestamptz    -- When the game was last played
```

#### Steam-Only Integration

- **Platform Adapter**: `steamPlatformAdapter` bridges Steam service with import system
- **Progress Tracking**: Real-time import progress with phase mapping
- **Error Handling**: Comprehensive error reporting and recovery
- **Import History**: Detailed import logs and statistics

#### Steam Import Flow

1. **API Key Storage**: Encrypted Steam API key storage in user preferences
2. **Profile Validation**: Verify Steam ID and profile accessibility
3. **Library Fetch**: Retrieve games with playtime and achievement data
4. **Game Processing**: Detailed metadata enrichment from Steam Store API
5. **Database Storage**: Store with proper Steam App ID tracking and import source
6. **Statistics**: Import statistics and duplicate handling

#### Configuration Requirements

```bash
# Required environment variable
VITE_STEAM_API_KEY=your_steam_api_key

# User configuration (stored encrypted)
Steam ID: User's Steam profile ID or custom URL
```

#### Usage Instructions

1. **Get Steam API Key**: Visit <https://steamcommunity.com/dev/apikey>
2. **Configure API Key**: Add to Settings → API Keys in the application
3. **Set Steam ID**: Add Steam profile ID in Settings → Library Import
4. **Profile Requirements**: Steam profile must be public for library access
5. **Import Library**: Use Library Import → Steam to sync games

#### Advanced Features

- **Playtime Tracking**: Imports exact playtime hours from Steam
- **Last Played**: Tracks when games were last played
- **Achievement Detection**: Identifies games with Steam achievements
- **Status Assignment**: Intelligent status assignment based on playtime patterns
- **Duplicate Prevention**: Prevents duplicate imports using Steam App IDs

The Steam integration is production-ready and provides a seamless import experience for users.

## Testing and Quality

### Testing Infrastructure (Fully Configured)

**✅ COMPLETE**: Testing infrastructure is set up and ready for development:

- **Test Setup File**: `src/test/setup.ts` configured with Vitest + React Testing Library
- **Test Files**: Multiple test files exist in `src/__tests__/` covering components, hooks, and utilities
- **Test Commands**: `npm test` and `npm run test:run` are fully functional

**Test Configuration Features**:

1. **Vitest** with React Testing Library and jsdom environment
2. **Test Coverage** reporting with v8 provider
3. **Mock Setup** for IntersectionObserver, ResizeObserver, and matchMedia
4. **Path Aliases** for clean imports in tests
5. **Automatic Cleanup** after each test case

**Running Tests**:
```bash
npm test            # Watch mode for development
npm run test:run    # Single run for CI
npm run test -- --coverage  # Run with coverage report
```

### Quality Requirements

- **Testing**: Vitest with React Testing Library (fully configured)
- **Linting**: ESLint configured with React-specific rules (MANDATORY - always run before pushing)
- **TypeScript**: Strict type checking enabled (MANDATORY - fix all errors)
- **Code Style**: Consistent formatting and naming conventions
- **Error Handling**: Comprehensive error boundaries and user feedback (MANDATORY)

## Development Workflow Checklist

Before completing any task, ALWAYS:

1. ✅ **Use MCP diagnostics** - FIRST run `mcp__ide__getDiagnostics` to check for errors
2. ✅ **Check database schema** - Review existing tables in Supabase before DB changes
3. ✅ **Use shadcn/ui components** - Never create custom UI components from scratch
4. ✅ **Use Roboto font** - Ensure typography consistency
5. ✅ **Use Lucide React icons** - Maintain icon consistency
6. ✅ **Implement responsive design** - Test on mobile, tablet, desktop
7. ✅ **Add loading states** - Show proper loading indicators
8. ✅ **Add error handling** - Handle all error cases gracefully
9. ✅ **Run ESLint** - Fix all linting errors and warnings (`npm run lint`)
10. ✅ **Fix TypeScript errors** - Ensure no TypeScript issues (strict mode)
11. ✅ **Run tests** - Execute `npm test` and ensure all tests pass
12. ✅ **Test functionality** - Verify features work as expected manually
13. ✅ **Push to GitHub** - Commit and push completed work

## Common Patterns to Follow

### Component Creation

```typescript
// ALWAYS use shadcn/ui as base
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// ALWAYS use Lucide React icons
import { Search, Plus, Star } from 'lucide-react'

// ALWAYS use proper TypeScript interfaces
interface ComponentProps {
  title: string;
  onClick: () => void;
}
```

### Database Operations

```typescript
// ALWAYS use Supabase helpers
import { db } from '@/lib/supabase'

// ALWAYS handle errors properly
try {
  const { data, error } = await db.games.create(gameData)
  if (error) throw error
  // Handle success
} catch (error) {
  // Handle error with user feedback
}
```

### Form Handling

```typescript
// ALWAYS use React Hook Form + Zod
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, 'Name is required')
})

const form = useForm({
  resolver: zodResolver(schema)
})
```

### State Management

```typescript
// ALWAYS use React Query for server state
import { useQuery, useMutation } from '@tanstack/react-query'

const { data, isLoading, error } = useQuery({
  queryKey: ['games'],
  queryFn: fetchGames
})
```

### MCP Tools Usage

```typescript
// ALWAYS use MCP diagnostics to check for errors
import { mcp__ide__getDiagnostics } from '@/tools/mcp'

// Check for TypeScript and linting errors
const diagnostics = await mcp__ide__getDiagnostics()

// Execute code in Jupyter kernels when needed
import { mcp__ide__executeCode } from '@/tools/mcp'

const result = await mcp__ide__executeCode({
  code: 'console.log("Testing MCP integration")'
})
```

## MCP Tools Integration Guide

### Available MCP Tools

1. **`mcp__ide__getDiagnostics`** - Get language diagnostics from VS Code
   - Use to check for TypeScript errors
   - Identify linting issues
   - Get real-time code quality feedback

2. **`mcp__ide__executeCode`** - Execute Python code in Jupyter kernel
   - Run data analysis scripts
   - Test API integrations
   - Execute utility functions

### When to Use MCP Tools

- **Before committing** - Always run `mcp__ide__getDiagnostics` to check for errors
- **During development** - Use diagnostics to catch issues early
- **For data processing** - Use `mcp__ide__executeCode` for data manipulation
- **API testing** - Execute code to test external service integrations

### MCP Tools Best Practices

- **ALWAYS prefer MCP tools** over manual CLI commands when available
- **USE diagnostics** before running traditional linting commands
- **INTEGRATE MCP** into your development workflow
- **LEVERAGE MCP** for enhanced IDE integration and error reporting

## IMPORTANT REMINDERS

- **Never create custom UI components** - Always use shadcn/ui
- **Never use other fonts** - Always use Roboto from Google Fonts
- **Never use other icons** - Always use Lucide React
- **Never skip linting** - Always run and fix ESLint issues
- **Never ignore TypeScript errors** - Always fix all type issues
- **Never skip tests** - Always run tests and ensure they pass (infrastructure is ready)
- **Never skip pushing** - Always push completed work to GitHub
- **Never skip database checks** - Always verify schema before DB operations
- **Never skip MCP diagnostics** - Always use `mcp__ide__getDiagnostics` to check for errors
- **Always use MCP tools** - Prefer MCP tools over manual CLI commands when available
