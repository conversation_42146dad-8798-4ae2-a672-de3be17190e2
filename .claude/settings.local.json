{"permissions": {"allow": ["WebFetch(domain:api-docs.igdb.com)", "Bash(npx supabase functions:*)", "Bash(npx supabase link:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npx supabase:*)", "Bash(npm run dev:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npm run lint:*)", "Bash(npx eslint:*)", "Bash(gh pr view:*)", "Bash(gh pr list:*)", "mcp__ide__getDiagnostics", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(timeout 10 npx supabase db push --dry-run)", "Bash(npx @supabase/cli@latest:*)", "Bash(npx:*)", "Bash(npx clear-npx-cache:*)", "Bash(sudo npm install:*)", "WebFetch(domain:api.thegamesdb.net)", "Bash(timeout 10s npm run dev)", "Bash(node:*)", "Bash(supabase functions deploy:*)", "Bash(supabase db:*)", "Bash(supabase migration:*)", "Bash(git reset:*)", "WebFetch(domain:github.com)", "Bash(echo $VITE_SUPABASE_URL)", "<PERSON><PERSON>(env)", "Bash(supabase status:*)", "Bash(supabase link:*)", "Bash(supabase projects:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(supabase start:*)", "Bash(psql:*)", "Bash(timeout 30 npx supabase start --debug)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "<PERSON><PERSON>(mv:*)", "mcp__supabase__apply_migration", "mcp__supabase__list_migrations", "Bash(cp:*)", "mcp__ide__executeCode", "Bash(SUPABASE_ACCESS_TOKEN=\"********************************************\" supabase migration list)", "<PERSON><PERSON>(curl:*)", "mcp__supabase__deploy_edge_function", "Bash(SUPABASE_ACCESS_TOKEN=\"********************************************\" supabase functions deploy steam-proxy --project-ref kfzwgkzvlbyxotnbhgqk)", "<PERSON><PERSON>(source .env)", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__get_logs", "mcp__supabase__get_advisors", "mcp__supabase__list_edge_functions", "<PERSON><PERSON>(pkill:*)", "Bash(pgrep:*)", "Bash(timeout 8s npm run dev)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(true)", "mcp__shadcn-ui__list_components", "Bash(git fetch:*)", "Bash(git cherry-pick:*)", "<PERSON><PERSON>(touch:*)", "Bash(supabase functions logs:*)", "Bash(supabase logs:*)", "Bash(supabase functions:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}