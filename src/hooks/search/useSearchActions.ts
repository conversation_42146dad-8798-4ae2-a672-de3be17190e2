import { useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { enhancedSearchService, SearchResult } from '../../lib/enhancedSearchService';
import { SearchFilters } from './useSearchState';

export function useSearchActions() {
  const { user } = useAuth();

  // Enhanced search mutation
  const searchMutation = useMutation({
    mutationFn: async (query: string): Promise<SearchResult> => {
      if (!query.trim()) {
        throw new Error('Search query is required');
      }
      return enhancedSearchService.enhancedSearch(query, user?.id);
    },
    onSuccess: (result) => {
      // Show success message with search stats
      const gameCount = result.games.length;
      const searchTime = result.searchTime;
      toast.success(`Found ${gameCount} games in ${searchTime}ms`, {
        duration: 2000
      });
    },
    onError: (error) => {
      // Enhanced error logging for search
      const errorDetails = {
        message: error?.message || 'Unknown search error',
        name: error?.name || 'SearchError',
        stack: error?.stack || 'No stack trace',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'enhancedSearch',
        context: 'useSearchActions',
        query: 'search query'
      };
      
      console.error('❌ Enhanced Search Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Search failed';
      toast.error(errorMessage);
    }
  });

  // Voice search mutation
  const voiceSearchMutation = useMutation({
    mutationFn: async (transcript: string): Promise<SearchResult> => {
      return enhancedSearchService.processVoiceSearch(transcript);
    },
    onSuccess: (result) => {
      toast.success(`Voice search complete: "${result.intent.originalQuery}"`);
    },
    onError: (error) => {
      // Enhanced error logging for voice search
      const errorDetails = {
        message: error?.message || 'Unknown voice search error',
        name: error?.name || 'VoiceSearchError',
        stack: error?.stack || 'No stack trace',
        timestamp: new Date().toISOString(),
        userId: user?.id || 'anonymous',
        action: 'voiceSearch',
        context: 'useSearchActions'
      };
      
      console.error('❌ Voice Search Error:', errorDetails);
      
      // Log to external service if available
      if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
        try {
          window.navigator.sendBeacon('/api/log-error', JSON.stringify(errorDetails));
        } catch (logError) {
          console.warn('Failed to send error log:', logError);
        }
      }
      
      toast.error('Voice search failed');
    }
  });

  // Perform search
  const performSearch = useCallback((query: string) => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }
    searchMutation.mutate(query);
  }, [searchMutation]);

  // Apply filters to current search
  const applyFilters = useCallback((filters: SearchFilters, currentQuery: string) => {
    // If we have a current query, re-search with filters
    if (currentQuery) {
      // For now, we'll just re-run the search
      // In a more advanced implementation, we'd modify the search with filters
      performSearch(currentQuery);
    }
  }, [performSearch]);

  // Search with suggestion
  const searchWithSuggestion = useCallback((suggestion: string) => {
    performSearch(suggestion);
  }, [performSearch]);

  // Clear search history
  const clearSearchHistory = useCallback(() => {
    if (user?.id) {
      localStorage.removeItem(`search_history_${user.id}`);
      toast.success('Search history cleared');
    }
  }, [user?.id]);

  return {
    // Mutations
    searchMutation,
    voiceSearchMutation,
    
    // Actions
    performSearch,
    applyFilters,
    searchWithSuggestion,
    clearSearchHistory,
    
    // State
    isSearching: searchMutation.isPending,
    searchError: searchMutation.error,
    searchResults: searchMutation.data,
    isVoiceSearching: voiceSearchMutation.isPending,
    voiceSearchError: voiceSearchMutation.error
  };
}