import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/base';
import { 
  Gamepad2, 
  BarChart3, 
  FolderPlus, 
  Filter, 
  Target, 
  Download,
  ImageIcon
} from '@/lib/icons';

interface LibraryTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  children: React.ReactNode;
}

export function LibraryTabs({ activeTab, onTabChange, children }: LibraryTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      <TabsList className="grid w-full grid-cols-7">
        <TabsTrigger value="library" className="flex items-center gap-2">
          <Gamepad2 className="h-4 w-4" />
          Library
        </TabsTrigger>
        <TabsTrigger value="stats" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          Stats
        </TabsTrigger>
        <TabsTrigger value="collections" className="flex items-center gap-2">
          <FolderPlus className="h-4 w-4" />
          Collections
        </TabsTrigger>
        <TabsTrigger value="filters" className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filters
        </TabsTrigger>
        <TabsTrigger value="analytics" className="flex items-center gap-2">
          <Target className="h-4 w-4" />
          Analytics
        </TabsTrigger>
        <TabsTrigger value="export" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export
        </TabsTrigger>
        <TabsTrigger value="artwork" className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4" />
          Artwork
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
}

export interface BaseTabProps {
  isActive: boolean;
}

export function TabContent({ 
  value, 
  children, 
  className = "space-y-6" 
}: { 
  value: string; 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <TabsContent value={value} className={className}>
      {children}
    </TabsContent>
  );
}