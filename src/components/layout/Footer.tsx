import { Heart, Code, Database, Gamepad2, Github, Twitter, Mail, ExternalLink, Star, Users, Zap } from 'lucide-react';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/base/tooltip';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const features = [
    { icon: Database, label: 'Multi-API Integration', description: 'IGDB & TheGamesDB' },
    { icon: Gamepad2, label: 'Multi-Platform Support', description: 'All gaming platforms' },
    { icon: Zap, label: 'AI-Powered Features', description: 'Smart recommendations' },
    { icon: Code, label: 'Modern UI/UX', description: 'Built with React & TypeScript' },
    { icon: Users, label: 'Community Driven', description: 'Open source project' },
    { icon: Star, label: 'Premium Experience', description: 'No ads, no tracking' },
  ];

  const apis = [
    { name: 'IGDB API', color: 'bg-blue-100 text-blue-800 border-blue-200', description: 'Internet Game Database' },
    { name: 'TheGamesDB API', color: 'bg-green-100 text-green-800 border-green-200', description: 'Comprehensive game metadata' },
    { name: 'Steam API', color: 'bg-slate-100 text-slate-800 border-slate-200', description: 'Steam integration' },
    { name: 'AI Services', color: 'bg-purple-100 text-purple-800 border-purple-200', description: 'Smart recommendations' },
  ];

  const socialLinks = [
    { icon: Github, label: 'GitHub', href: '#', color: 'hover:text-slate-600' },
    { icon: Twitter, label: 'Twitter', href: '#', color: 'hover:text-blue-500' },
    { icon: Mail, label: 'Contact', href: 'mailto:<EMAIL>', color: 'hover:text-green-600' },
  ];

  return (
    <footer className="bg-background border-t border-border mt-20 shadow-2xl">
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-1 space-y-6">
            <div className="flex items-center gap-3 group">
              <div className="p-3 bg-gradient-to-br from-primary/15 via-secondary/10 to-accent/15 rounded-xl border border-primary/30 group-hover:border-primary/50 transition-all duration-300 group-hover:scale-110 shadow-sm group-hover:shadow-md">
                <Gamepad2 className="h-7 w-7 text-primary group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
                Game Library
              </h3>
            </div>
            <p className="text-muted-foreground leading-relaxed text-sm">
              The ultimate gaming companion for discovering, organizing, and tracking your game collection across all platforms. Powered by AI and comprehensive game databases.
            </p>
            
            {/* Social Links */}
            <div className="flex items-center gap-3">
              {socialLinks.map((social) => (
                <Tooltip key={social.label}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-9 w-9 rounded-full hover:bg-accent/50 transition-all duration-200 hover:scale-110 ${social.color}`}
                      asChild
                    >
                      <a href={social.href} target="_blank" rel="noopener noreferrer">
                        <social.icon className="h-4 w-4" />
                        <span className="sr-only">{social.label}</span>
                      </a>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{social.label}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </div>

          {/* Features Section */}
          <div className="lg:col-span-1 space-y-6">
            <h4 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              Features
            </h4>
            <ul className="space-y-3">
              {features.map((feature, index) => (
                <li key={index} className="group">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/30 transition-all duration-200 cursor-help">
                        <div className="p-1.5 bg-primary/10 rounded-md group-hover:bg-primary/20 transition-colors">
                          <feature.icon className="h-4 w-4 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-foreground">{feature.label}</div>
                          <div className="text-xs text-muted-foreground">{feature.description}</div>
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{feature.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
              ))}
            </ul>
          </div>

          {/* APIs Section */}
          <div className="lg:col-span-1 space-y-6">
            <h4 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Database className="h-5 w-5 text-primary" />
              Powered By
            </h4>
            <div className="space-y-3">
              {apis.map((api, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <Badge 
                      variant="secondary" 
                      className={`${api.color} hover:scale-105 transition-transform cursor-help w-full justify-start p-3 h-auto`}
                    >
                      <Database className="h-3 w-3 mr-2" />
                      <div className="text-left">
                        <div className="font-medium">{api.name}</div>
                        <div className="text-xs opacity-80">{api.description}</div>
                      </div>
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{api.description}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="lg:col-span-1 space-y-6">
            <h4 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <ExternalLink className="h-5 w-5 text-primary" />
              Quick Links
            </h4>
            <div className="space-y-2">
              {[
                { label: 'Privacy Policy', href: '/privacy' },
                { label: 'Terms of Service', href: '/terms' },
                { label: 'API Documentation', href: '/docs' },
                { label: 'Support Center', href: '/support' },
                { label: 'Feature Requests', href: '/feedback' },
                { label: 'System Status', href: '/status' },
              ].map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="block text-sm text-muted-foreground hover:text-primary transition-colors duration-200 hover:translate-x-1 transform"
                >
                  {link.label}
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="border-t border-border/50 mt-12 pt-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
            {[
              { label: 'Games Tracked', value: '50K+', icon: Gamepad2 },
              { label: 'Active Users', value: '1.2K+', icon: Users },
              { label: 'API Calls', value: '100K+', icon: Database },
              { label: 'Uptime', value: '99.9%', icon: Zap },
            ].map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="flex items-center justify-center mb-2">
                  <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                    <stat.icon className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border/50 pt-8 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              Made with <Heart className="h-4 w-4 text-red-500 animate-pulse" /> for gamers everywhere
            </p>
            <Badge variant="outline" className="text-xs">
              v2.1.0
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            © {currentYear} Game Library. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
