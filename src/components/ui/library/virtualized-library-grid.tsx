import { memo, useMemo, useRef, useEffect, useState } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { UnifiedGameCard } from '@/components/ui/game/unified-game-card';
import { ViewSettings } from '@/components/ui/library/view-customization-controls';
import { UserGameWithDetails } from '@/types/database';
import { cn } from '@/lib/utils';

interface VirtualizedLibraryGridProps {
  games: UserGameWithDetails[];
  viewSettings: ViewSettings;
  onGameClick?: (gameData: UserGameWithDetails) => void;
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onRemoveGame?: (userGameId: string) => void;
  onSelect?: (gameId: string, selected: boolean) => void;
  selectedGames?: Set<string>;
  enableSelection?: boolean;
  className?: string;
}

interface GridItemData {
  games: UserGameWithDetails[];
  columnsPerRow: number;
  viewSettings: ViewSettings;
  onGameClick?: (gameData: UserGameWithDetails) => void;
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onRemoveGame?: (userGameId: string) => void;
  onSelect?: (gameId: string, selected: boolean) => void;
  selectedGames?: Set<string>;
  enableSelection?: boolean;
  cardWidth: number;
  gap: number;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: GridItemData;
}

// Calculate grid dimensions based on view settings with responsive breakpoints
const getGridDimensions = (viewSettings: ViewSettings, containerWidth: number) => {
  // Responsive base card widths that adapt to screen size
  const getBaseCardWidth = (variant: string, width: number) => {
    const sizes = {
      compact: {
        mobile: 140,
        tablet: 160,
        desktop: 180,
      },
      standard: {
        mobile: 160,
        tablet: 200,
        desktop: 220,
      },
      detailed: {
        mobile: 180,
        tablet: 240,
        desktop: 280,
      },
    };

    const config = sizes[variant as keyof typeof sizes];
    if (width < 640) return config.mobile;      // Mobile
    if (width < 1024) return config.tablet;    // Tablet
    return config.desktop;                      // Desktop
  };

  const gapSizes = {
    tight: containerWidth < 640 ? 4 : 8,
    normal: containerWidth < 640 ? 8 : 16,
    spacious: containerWidth < 640 ? 12 : 24,
  };

  const cardWidth = getBaseCardWidth(viewSettings.variant, containerWidth);
  const gap = gapSizes[viewSettings.gridDensity];
  
  // Calculate number of columns based on container width with responsive limits
  const availableWidth = containerWidth - (gap * 2);
  let columnsPerRow = Math.floor(availableWidth / (cardWidth + gap));
  
  // Responsive column limits
  const getColumnLimits = (variant: string, width: number) => {
    if (width < 640) {
      // Mobile: 1-3 columns max
      return { min: 1, max: variant === 'detailed' ? 2 : 3 };
    } else if (width < 1024) {
      // Tablet: 2-5 columns max
      return { min: 2, max: variant === 'detailed' ? 3 : 5 };
    } else {
      // Desktop: full range
      return { 
        min: 1, 
        max: variant === 'compact' ? 8 : variant === 'detailed' ? 4 : 6 
      };
    }
  };

  const { min: minColumns, max: maxColumns } = getColumnLimits(viewSettings.variant, containerWidth);
  columnsPerRow = Math.max(minColumns, Math.min(maxColumns, columnsPerRow));
  
  // Calculate actual card width to fill available space
  const totalGapWidth = (columnsPerRow - 1) * gap;
  const actualCardWidth = (availableWidth - totalGapWidth) / columnsPerRow;
  
  // Calculate card height based on aspect ratio with responsive adjustments
  const aspectRatios = {
    compact: containerWidth < 640 ? 1.2 : 4/3,
    standard: containerWidth < 640 ? 1.4 : 3/4,
    detailed: containerWidth < 640 ? 1.5 : 2/3,
  };
  
  const cardHeight = actualCardWidth / aspectRatios[viewSettings.variant];
  
  // Add responsive padding for text content
  const textPadding = containerWidth < 640 ? 20 : 40;
  
  return {
    columnsPerRow,
    cardWidth: actualCardWidth,
    cardHeight: cardHeight + textPadding,
    gap,
  };
};

// Memoized grid item component
const GridItem = memo<GridItemProps>(({ columnIndex, rowIndex, style, data }) => {
  const { 
    games, 
    columnsPerRow, 
    viewSettings, 
    onGameClick, 
    onStatusUpdate, 
    onRemoveGame,
    onSelect,
    selectedGames,
    enableSelection,
    cardWidth,
    gap 
  } = data;
  
  const gameIndex = rowIndex * columnsPerRow + columnIndex;
  const game = games[gameIndex];

  if (!game) {
    return <div style={style} />;
  }

  return (
    <div
      style={{
        ...style,
        padding: gap / 2,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start'
      }}
    >
      <div style={{ width: cardWidth }}>
        <UnifiedGameCard
          gameData={game}
          variant={viewSettings.variant}
          style={viewSettings.style}
          animationLevel={viewSettings.animationLevel}
          showQuickActions={true}
          enableHoverEffects={viewSettings.enableHoverEffects}
          enableSelection={enableSelection}
          isSelected={selectedGames?.has(game.id)}
          onGameClick={onGameClick}
          onStatusUpdate={onStatusUpdate}
          onRemoveGame={onRemoveGame}
          onSelect={onSelect}
          className="w-full max-w-none"
        />
      </div>
    </div>
  );
});

GridItem.displayName = 'GridItem';

// Hook for container size observation
function useContainerSize() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(container);
    
    // Initial size
    const rect = container.getBoundingClientRect();
    setSize({ width: rect.width, height: rect.height });

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { containerRef, size };
}

export const VirtualizedLibraryGrid = memo<VirtualizedLibraryGridProps>(({
  games,
  viewSettings,
  onGameClick,
  onStatusUpdate,
  onRemoveGame,
  onSelect,
  selectedGames = new Set(),
  enableSelection = false,
  className
}) => {
  const { containerRef, size } = useContainerSize();
  
  const gridDimensions = useMemo(() => {
    if (size.width === 0) return null;
    return getGridDimensions(viewSettings, size.width);
  }, [viewSettings, size.width]);

  // Memoized data for grid items
  const itemData = useMemo(() => {
    if (!gridDimensions) return null;
    
    return {
      games,
      columnsPerRow: gridDimensions.columnsPerRow,
      viewSettings,
      onGameClick,
      onStatusUpdate,
      onRemoveGame,
      onSelect,
      selectedGames,
      enableSelection,
      cardWidth: gridDimensions.cardWidth,
      gap: gridDimensions.gap,
    };
  }, [
    games,
    gridDimensions,
    viewSettings,
    onGameClick,
    onStatusUpdate,
    onRemoveGame,
    onSelect,
    selectedGames,
    enableSelection
  ]);

  // Loading state
  if (size.width === 0 || !gridDimensions || !itemData) {
    return (
      <div ref={containerRef} className={cn('w-full h-full min-h-96', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-pulse">
            <div className="grid grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="w-48 h-64 bg-muted rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (games.length === 0) {
    return (
      <div ref={containerRef} className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <p className="text-lg text-muted-foreground">No games found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search criteria
          </p>
        </div>
      </div>
    );
  }

  const { columnsPerRow, cardHeight } = gridDimensions;
  const rowCount = Math.ceil(games.length / columnsPerRow);

  return (
    <div 
      ref={containerRef} 
      className={cn('w-full h-full', className)}
      role="grid"
      aria-label={`Game library grid with ${games.length} games`}
    >
      <Grid
        columnCount={columnsPerRow}
        columnWidth={gridDimensions.cardWidth + gridDimensions.gap}
        height={size.height}
        rowCount={rowCount}
        rowHeight={cardHeight + gridDimensions.gap}
        width={size.width}
        itemData={itemData}
        overscanRowCount={2}
        overscanColumnCount={1}
        style={{
          overflowX: 'hidden'
        }}
      >
        {GridItem}
      </Grid>
    </div>
  );
});

VirtualizedLibraryGrid.displayName = 'VirtualizedLibraryGrid';

// Performance threshold - use virtualization for collections larger than this
export const VIRTUALIZATION_THRESHOLD = 50;