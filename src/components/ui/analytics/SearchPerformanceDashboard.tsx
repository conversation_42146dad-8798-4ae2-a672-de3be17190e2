import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { ScrollArea } from '@/components/ui/base/scroll-area';
import { useSearchAnalytics } from '@/hooks/useSearchAnalytics';
import { SearchInsightsPanel } from './SearchInsightsPanel';
import { 
  Search, 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Clock, 
  Target, 
  Zap,
  Users,
  Eye,
  MousePointer,
  Activity,
  CheckCircle2,
  XCircle,
  RefreshCw,
  Brain
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchPerformanceDashboardProps {
  className?: string;
}

interface TrendData {
  value: number;
  trend: 'up' | 'down' | 'stable';
  change: number;
}

export function SearchPerformanceDashboard({ className }: SearchPerformanceDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');

  const { 
    analytics, 
    queryHistory, 
    popularTerms, 
    interactions,
    isLoading,
    error,
    refetchAnalytics,
    isTracking,
    toggleTracking
  } = useSearchAnalytics();

  // Calculate trend data
  const trendData = useMemo(() => {
    if (!analytics) return null;

    // Mock trend calculations - in a real implementation, you'd compare with previous periods
    const calculateTrend = (current: number, baseline: number = 100): TrendData => {
      const change = ((current - baseline) / baseline) * 100;
      return {
        value: current,
        trend: change > 5 ? 'up' : change < -5 ? 'down' : 'stable',
        change: Math.abs(change)
      };
    };

    return {
      totalSearches: calculateTrend(analytics.total_searches),
      successRate: calculateTrend(analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100),
      responseTime: calculateTrend(analytics.average_response_time),
      cacheHitRate: calculateTrend(analytics.cache_hit_rate * 100),
      conversionRate: calculateTrend(analytics.conversion_rate * 100),
      interactions: calculateTrend(analytics.total_interactions)
    };
  }, [analytics]);

  // Calculate additional metrics
  const additionalMetrics = useMemo(() => {
    if (!analytics || !queryHistory || !interactions) return null;

    const failureRate = analytics.total_searches > 0 
      ? ((analytics.total_searches - analytics.successful_searches) / analytics.total_searches) * 100 
      : 0;

    const avgQueriesPerSession = queryHistory.length > 0 ? queryHistory.length / Math.max(1, analytics.total_searches) : 0;
    
    const popularQueries = analytics.top_queries?.slice(0, 5) || [];
    
    const recentInteractions = interactions.slice(0, 10);

    return {
      failureRate,
      avgQueriesPerSession,
      popularQueries,
      recentInteractions,
      searchVelocity: analytics.total_searches / Math.max(1, 30), // searches per day
      engagementScore: analytics.total_interactions / Math.max(1, analytics.total_searches)
    };
  }, [analytics, queryHistory, interactions]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatPercentage = (num: number) => `${num.toFixed(1)}%`;
  const formatTime = (ms: number) => `${ms.toFixed(0)}ms`;

  const TrendIcon = ({ trend }: { trend: 'up' | 'down' | 'stable' }) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-blue-600" />;
    }
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <XCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p>Error loading search analytics</p>
            <Button variant="outline" onClick={refetchAnalytics} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Activity className="h-8 w-8 mx-auto mb-2 animate-pulse" />
            <p>Loading search analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No search data available</p>
            <p className="text-sm">Start searching to see analytics</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            Search Performance Dashboard
          </h2>
          <p className="text-muted-foreground">
            Track and analyze your search behavior and performance
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Tracking:</span>
            <Button
              variant={isTracking ? "default" : "outline"}
              size="sm"
              onClick={() => toggleTracking(!isTracking)}
            >
              {isTracking ? <CheckCircle2 className="h-4 w-4 mr-1" /> : <XCircle className="h-4 w-4 mr-1" />}
              {isTracking ? 'Enabled' : 'Disabled'}
            </Button>
          </div>
          
          <Button variant="outline" onClick={refetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Insights
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Total Searches</p>
                    <p className="text-2xl font-bold">{formatNumber(analytics.total_searches)}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {trendData && <TrendIcon trend={trendData.totalSearches.trend} />}
                    <Search className="h-4 w-4 text-primary" />
                  </div>
                </div>
                {trendData && (
                  <div className="flex items-center mt-1">
                    <span className={cn(
                      "text-xs",
                      trendData.totalSearches.trend === 'up' ? "text-green-600" :
                      trendData.totalSearches.trend === 'down' ? "text-red-600" : "text-blue-600"
                    )}>
                      {trendData.totalSearches.change.toFixed(1)}% from last period
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Success Rate</p>
                    <p className="text-2xl font-bold">
                      {formatPercentage(analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100)}
                    </p>
                  </div>
                  <div className="flex items-center gap-1">
                    {trendData && <TrendIcon trend={trendData.successRate.trend} />}
                    <Target className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <Progress 
                  value={analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100} 
                  className="h-1 mt-2" 
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Avg Response Time</p>
                    <p className="text-2xl font-bold">{formatTime(analytics.average_response_time)}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {trendData && <TrendIcon trend={trendData.responseTime.trend} />}
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                <div className="mt-1">
                  <span className={cn(
                    "text-xs",
                    analytics.average_response_time < 1000 ? "text-green-600" :
                    analytics.average_response_time < 3000 ? "text-yellow-600" : "text-red-600"
                  )}>
                    {analytics.average_response_time < 1000 ? 'Excellent' :
                     analytics.average_response_time < 3000 ? 'Good' : 'Needs improvement'}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Cache Hit Rate</p>
                    <p className="text-2xl font-bold">{formatPercentage(analytics.cache_hit_rate * 100)}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {trendData && <TrendIcon trend={trendData.cacheHitRate.trend} />}
                    <Zap className="h-4 w-4 text-yellow-600" />
                  </div>
                </div>
                <Progress 
                  value={analytics.cache_hit_rate * 100} 
                  className="h-1 mt-2" 
                />
              </CardContent>
            </Card>
          </div>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Total Interactions</p>
                    <p className="text-lg font-semibold">{formatNumber(analytics.total_interactions)}</p>
                  </div>
                  <MousePointer className="h-4 w-4 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Conversion Rate</p>
                    <p className="text-lg font-semibold">{formatPercentage(analytics.conversion_rate * 100)}</p>
                  </div>
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Trending Score</p>
                    <p className="text-lg font-semibold">{analytics.trending_score.toFixed(1)}</p>
                  </div>
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Response Time</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{formatTime(analytics.average_response_time)}</span>
                      <Progress value={Math.min(100, (3000 - analytics.average_response_time) / 30)} className="w-20 h-2" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Cache Efficiency</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{formatPercentage(analytics.cache_hit_rate * 100)}</span>
                      <Progress value={analytics.cache_hit_rate * 100} className="w-20 h-2" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Success Rate</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">
                        {formatPercentage(analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100)}
                      </span>
                      <Progress value={analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100} className="w-20 h-2" />
                    </div>
                  </div>

                  {additionalMetrics && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Failure Rate</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{formatPercentage(additionalMetrics.failureRate)}</span>
                        <Progress value={additionalMetrics.failureRate} className="w-20 h-2" />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Top Queries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Popular Search Terms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {analytics.top_queries && analytics.top_queries.length > 0 ? (
                      analytics.top_queries.slice(0, 10).map((query, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <span className="text-sm font-medium truncate flex-1">{query}</span>
                          <Badge variant="secondary" className="text-xs">
                            #{index + 1}
                          </Badge>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <Search className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No search data available</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Behavior Tab */}
        <TabsContent value="behavior" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Engagement */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {additionalMetrics && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Engagement Score</span>
                        <span className="text-lg font-bold">
                          {additionalMetrics.engagementScore.toFixed(2)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Search Velocity</span>
                        <span className="text-sm">
                          {additionalMetrics.searchVelocity.toFixed(1)} searches/day
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Avg Queries/Session</span>
                        <span className="text-sm">
                          {additionalMetrics.avgQueriesPerSession.toFixed(1)}
                        </span>
                      </div>
                    </>
                  )}

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Conversion Rate</span>
                    <span className="text-sm">
                      {formatPercentage(analytics.conversion_rate * 100)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Recent Search Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {queryHistory && queryHistory.length > 0 ? (
                      queryHistory.slice(0, 10).map((query) => (
                        <div key={query.id} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
                          <Search className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm truncate flex-1">{query.query_text}</span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(query.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <Activity className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No recent activity</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Community Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Community Trending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {popularTerms && popularTerms.length > 0 ? (
                      popularTerms.slice(0, 10).map((term, termIndex) => (
                        <div key={term.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-muted-foreground">#{termIndex + 1}</span>
                            <span className="text-sm font-medium">{term.query_text}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {term.search_count}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {formatPercentage(term.success_rate)}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <TrendingUp className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No trending data available</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Performance Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      Search Performance
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-300">
                      Your average response time is {formatTime(analytics.average_response_time)}
                      {analytics.average_response_time < 1000 ? ' - Excellent!' : 
                       analytics.average_response_time < 3000 ? ' - Good performance' : ' - Could be improved'}
                    </p>
                  </div>

                  <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      Cache Efficiency
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-300">
                      {formatPercentage(analytics.cache_hit_rate * 100)} of searches use cached results, 
                      {analytics.cache_hit_rate > 0.5 ? ' saving time and resources' : ' room for improvement'}
                    </p>
                  </div>

                  <div className="p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800">
                    <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                      Search Success
                    </p>
                    <p className="text-xs text-purple-600 dark:text-purple-300">
                      {formatPercentage(analytics.successful_searches / Math.max(analytics.total_searches, 1) * 100)} success rate
                      {analytics.successful_searches / Math.max(analytics.total_searches, 1) > 0.9 ? ' - Excellent!' : ' - Keep exploring!'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* AI Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <SearchInsightsPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
}