import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Database,
  FileSpreadsheet,
  Info
} from 'lucide-react';
import { csvImportService, CSVGameData, ImportResult, ExportOptions } from '@/lib/csvImportService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { cn } from '@/lib/utils';

interface CSVImportCardProps {
  className?: string;
  onImportComplete?: (result: ImportResult) => void;
  onExportComplete?: (csv: string) => void;
}

export const CSVImportCard: React.FC<CSVImportCardProps> = ({
  className = '',
  onImportComplete,
  onExportComplete
}) => {
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    gameCount: number;
  } | null>(null);
  const [parsedData, setParsedData] = useState<CSVGameData[]>([]);
  const [importOptions, setImportOptions] = useState({
    skipDuplicates: true,
    updateExisting: false
  });
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeStats: true,
    includeNotes: true,
    includeMetadata: true,
    format: 'csv' as const
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }

    try {
      const csvContent = await file.text();
      
      // Validate CSV
      const validation = csvImportService.validateCSV(csvContent);
      setValidationResult(validation);
      
      if (validation.isValid) {
        // Parse CSV data
        const data = csvImportService.parseCSV(csvContent);
        setParsedData(data);
        
        toast.success(`CSV validated successfully! Found ${validation.gameCount} games.`);
        
        if (validation.warnings.length > 0) {
          toast.error(`${validation.warnings.length} warnings found. Check details below.`);
        }
      } else {
        toast.error(`CSV validation failed: ${validation.errors.join(', ')}`);
        setParsedData([]);
      }
    } catch (error) {
      console.error('File reading error:', error);
      toast.error('Failed to read CSV file');
      setValidationResult(null);
      setParsedData([]);
    }
  };

  const handleImport = async () => {
    if (!user?.id) {
      toast.error('Please log in to import your data');
      return;
    }

    if (!parsedData.length) {
      toast.error('No valid data to import');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await csvImportService.importCSVData(
        user.id,
        parsedData,
        importOptions
      );

      clearInterval(progressInterval);
      setImportProgress(100);

      if (result.success) {
        toast.success(
          `Import completed! ${result.imported} games imported, ${result.skipped} skipped.`
        );
        
        if (result.errors.length > 0) {
          toast.error(`${result.errors.length} errors occurred during import.`);
        }
      } else {
        toast.error(`Import failed: ${result.errors.join(', ')}`);
      }

      if (onImportComplete) {
        onImportComplete(result);
      }

      // Reset state
      setTimeout(() => {
        setParsedData([]);
        setValidationResult(null);
        setImportProgress(0);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }, 2000);

    } catch (error) {
      console.error('Import error:', error);
      toast.error('Import failed unexpectedly');
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = async () => {
    if (!user?.id) {
      toast.error('Please log in to export your data');
      return;
    }

    setIsExporting(true);

    try {
      const csvData = await csvImportService.exportUserCollection(user.id, exportOptions);
      
      // Create download
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `codexa-collection-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Collection exported successfully!');

      if (onExportComplete) {
        onExportComplete(csvData);
      }

    } catch (error) {
      console.error('Export error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Export failed';
      toast.error(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const downloadTemplate = () => {
    const template = csvImportService.generateTemplate();
    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'codexa-import-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    toast.success('Template downloaded successfully!');
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-green-600" />
            CSV Import/Export
          </div>
          {validationResult && (
            <Badge 
              variant={validationResult.isValid ? "default" : "destructive"}
              className="text-xs"
            >
              {validationResult.isValid ? "Valid" : "Invalid"} CSV
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Import Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4 text-blue-600" />
              <h3 className="font-medium">Import Games</h3>
            </div>

            {/* File Upload */}
            <div className="space-y-3">
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="hidden"
              />
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isImporting}
                  className="flex-1"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose CSV File
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={downloadTemplate}
                  className="text-xs"
                >
                  <FileText className="h-3 w-3 mr-1" />
                  Template
                </Button>
              </div>

              {/* Import Options */}
              <div className="flex gap-4 text-sm">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={importOptions.skipDuplicates}
                    onChange={(e) => setImportOptions(prev => ({
                      ...prev,
                      skipDuplicates: e.target.checked
                    }))}
                    className="rounded"
                  />
                  Skip duplicates
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={importOptions.updateExisting}
                    onChange={(e) => setImportOptions(prev => ({
                      ...prev,
                      updateExisting: e.target.checked
                    }))}
                    className="rounded"
                  />
                  Update existing
                </label>
              </div>
            </div>

            {/* Validation Results */}
            {validationResult && (
              <div className={cn(
                "p-3 rounded-lg border",
                validationResult.isValid 
                  ? "bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800"
                  : "bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800"
              )}>
                <div className="flex items-center gap-2 mb-2">
                  {validationResult.isValid ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={cn(
                    "font-medium text-sm",
                    validationResult.isValid ? "text-green-800 dark:text-green-200" : "text-red-800 dark:text-red-200"
                  )}>
                    {validationResult.isValid ? "CSV is valid" : "CSV has errors"}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {validationResult.gameCount} games
                  </Badge>
                </div>

                {validationResult.errors.length > 0 && (
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-red-700 dark:text-red-300">Errors:</p>
                    {validationResult.errors.map((error, index) => (
                      <p key={index} className="text-xs text-red-600 dark:text-red-400">• {error}</p>
                    ))}
                  </div>
                )}

                {validationResult.warnings.length > 0 && (
                  <div className="space-y-1 mt-2">
                    <p className="text-xs font-medium text-orange-700 dark:text-orange-300">Warnings:</p>
                    {validationResult.warnings.map((warning, index) => (
                      <p key={index} className="text-xs text-orange-600 dark:text-orange-400">• {warning}</p>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Importing games...</span>
                  <span className="text-sm text-muted-foreground">{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="h-2" />
              </div>
            )}

            {/* Import Button */}
            <Button
              onClick={handleImport}
              disabled={isImporting || !validationResult?.isValid || parsedData.length === 0}
              className="w-full"
            >
              {isImporting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Import {parsedData.length} Games
                </>
              )}
            </Button>
          </div>

          {/* Divider */}
          <div className="border-t" />

          {/* Export Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Download className="h-4 w-4 text-green-600" />
              <h3 className="font-medium">Export Collection</h3>
            </div>

            {/* Export Options */}
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeStats}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      includeStats: e.target.checked
                    }))}
                    className="rounded"
                  />
                  Include statistics
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeNotes}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      includeNotes: e.target.checked
                    }))}
                    className="rounded"
                  />
                  Include notes
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeMetadata}
                    onChange={(e) => setExportOptions(prev => ({
                      ...prev,
                      includeMetadata: e.target.checked
                    }))}
                    className="rounded"
                  />
                  Include metadata
                </label>
              </div>

              <Button
                onClick={handleExport}
                disabled={isExporting}
                variant="outline"
                className="w-full"
              >
                {isExporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export Collection
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Information */}
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                  CSV Format Tips:
                </p>
                <ul className="text-blue-700 dark:text-blue-300 space-y-1 text-xs">
                  <li>• Use the template for proper column headers</li>
                  <li>• Game name is required, other fields are optional</li>
                  <li>• Status: playing, completed, backlog, wishlist, library</li>
                  <li>• Rating: 0-10 scale</li>
                  <li>• Dates: YYYY-MM-DD format recommended</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface DataPortabilityOverviewProps {
  className?: string;
}

export const DataPortabilityOverview: React.FC<DataPortabilityOverviewProps> = ({ 
  className = '' 
}) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5 text-blue-600" />
          Data Portability
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Import Stats */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                <Upload className="h-5 w-5 text-blue-600" />
                <span className="font-medium">Import Data</span>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• CSV files supported</li>
                <li>• Automatic validation</li>
                <li>• Duplicate detection</li>
                <li>• Bulk import capability</li>
              </ul>
            </div>

            {/* Export Stats */}
            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                <Download className="h-5 w-5 text-green-600" />
                <span className="font-medium">Export Data</span>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Complete collection export</li>
                <li>• Customizable fields</li>
                <li>• Statistics included</li>
                <li>• Cross-platform compatible</li>
              </ul>
            </div>
          </div>

          {/* Supported Formats */}
          <div className="p-3 border rounded-lg">
            <h4 className="font-medium text-sm mb-2">Supported Formats</h4>
            <div className="flex gap-2">
              <Badge variant="secondary">CSV</Badge>
              <Badge variant="outline" className="text-xs">JSON (coming soon)</Badge>
              <Badge variant="outline" className="text-xs">XML (coming soon)</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};