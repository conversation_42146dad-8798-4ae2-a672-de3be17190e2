/**
 * Artwork API Key Manager Component
 * Manages API keys for different artwork services with intelligent routing
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { But<PERSON> } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Label } from '@/components/ui/base/label';
import { Badge } from '@/components/ui/base/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { Switch } from '@/components/ui/base/switch';
import { 
  Key, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  Activity,
  Zap,
  Settings
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { apiKeyStore, ArtworkService, KeyType } from '@/lib/apiKeyStore';
import { rateLimitManager } from '@/lib/services/rateLimitManager';
import { artworkAPIRouter } from '@/lib/services/artworkAPIRouter';

interface ServiceKeyEntry {
  id: string;
  value: string;
  label?: string;
  source: 'user' | 'env';
  isVisible: boolean;
  usage?: {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    lastUsed: number;
  };
}

interface ServiceInfo {
  service: ArtworkService;
  displayName: string;
  keyType: KeyType;
  keyLabel: string;
  description: string;
  costPerRequest: number;
  qualityRating: number;
  features: string[];
  isRequired: boolean;
}

const ArtworkAPIKeyManager: React.FC = () => {
  const [serviceKeys, setServiceKeys] = useState<Record<ArtworkService, ServiceKeyEntry[]>>({} as Record<ArtworkService, ServiceKeyEntry[]>);
  const [newKeyValues, setNewKeyValues] = useState<Record<string, string>>({});
  const [newKeyLabels, setNewKeyLabels] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<Record<string, boolean>>({});
  const [statistics, setStatistics] = useState<Record<string, unknown> | null>(null);
  const [queueStatus, setQueueStatus] = useState<Record<string, unknown> | null>(null);
  
  const servicesInfo: ServiceInfo[] = useMemo(() => [
    {
      service: 'igdb',
      displayName: 'IGDB (Internet Game Database)',
      keyType: 'client_id',
      keyLabel: 'Client ID',
      description: 'Official game metadata, covers, and artwork',
      costPerRequest: 0,
      qualityRating: 5,
      features: ['Game Search', 'Official Covers', 'Artwork', 'Screenshots'],
      isRequired: true
    },
    {
      service: 'steamgriddb',
      displayName: 'SteamGridDB',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'High-quality community artwork and covers',
      costPerRequest: 0,
      qualityRating: 5,
      features: ['Custom Covers', 'High-Res Artwork', 'Community Content'],
      isRequired: false
    },
    {
      service: 'thegamesdb',
      displayName: 'TheGamesDB',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Official game database with metadata and artwork',
      costPerRequest: 0,
      qualityRating: 4,
      features: ['Game Search', 'Official Data', 'Covers & Screenshots'],
      isRequired: false
    },
    {
      service: 'serpapi',
      displayName: 'SerpAPI',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Web search for finding artwork across the internet',
      costPerRequest: 0.01,
      qualityRating: 3,
      features: ['Web Search', 'Bulk Search', 'Image Discovery'],
      isRequired: false
    },
    {
      service: 'openai',
      displayName: 'OpenAI',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'AI-powered artwork search and analysis',
      costPerRequest: 0.02,
      qualityRating: 4,
      features: ['Smart Search', 'Content Analysis', 'Quality Assessment'],
      isRequired: false
    },
    {
      service: 'deepseek',
      displayName: 'DeepSeek',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Alternative AI provider for artwork search',
      costPerRequest: 0.005,
      qualityRating: 3,
      features: ['AI Search', 'Cost Effective'],
      isRequired: false
    },
    {
      service: 'gemini',
      displayName: 'Google Gemini',
      keyType: 'api_key',
      keyLabel: 'API Key',
      description: 'Google\'s AI for intelligent artwork discovery',
      costPerRequest: 0.01,
      qualityRating: 4,
      features: ['Smart Analysis', 'Image Recognition'],
      isRequired: false
    }
  ], []);

  const loadServiceKeys = useCallback(async () => {
    setLoading(true);
    const allKeys: Record<ArtworkService, ServiceKeyEntry[]> = {} as Record<ArtworkService, ServiceKeyEntry[]>;

    try {
      for (const serviceInfo of servicesInfo) {
        const keys = await apiKeyStore.getAllAvailableServiceKeys(
          serviceInfo.service, 
          serviceInfo.keyType
        );

        allKeys[serviceInfo.service] = keys.map(key => ({
          ...key,
          isVisible: false,
          usage: rateLimitManager.getUsageStats(serviceInfo.service, key.id) || undefined
        }));
      }

      setServiceKeys(allKeys);
    } catch (error) {
      console.error('Failed to load service keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  }, [servicesInfo]);

  const loadStatistics = useCallback(async () => {
    try {
      const stats = artworkAPIRouter.getServiceStatistics();
      const queue = artworkAPIRouter.getQueueStatus();
      setStatistics(stats);
      setQueueStatus(queue);
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  }, []);

  useEffect(() => {
    loadServiceKeys();
    loadStatistics();

    // Refresh statistics every 10 seconds
    const interval = setInterval(loadStatistics, 10000);
    return () => clearInterval(interval);
  }, [loadServiceKeys, loadStatistics]);

  const handleAddKey = async (service: ArtworkService, keyType: KeyType) => {
    const keyName = `${service}_${keyType}`;
    const keyValue = newKeyValues[keyName]?.trim();
    const keyLabel = newKeyLabels[keyName]?.trim();

    if (!keyValue) {
      toast.error('Please enter an API key');
      return;
    }

    setSaving(prev => ({ ...prev, [keyName]: true }));

    try {
      await apiKeyStore.storeKey({
        service,
        keyType,
        value: keyValue,
        label: keyLabel || `${service.toUpperCase()} Key`
      });

      setNewKeyValues(prev => ({ ...prev, [keyName]: '' }));
      setNewKeyLabels(prev => ({ ...prev, [keyName]: '' }));
      
      await loadServiceKeys();
      toast.success('API key added successfully');
    } catch (error) {
      console.error('Failed to add API key:', error);
      toast.error('Failed to add API key');
    } finally {
      setSaving(prev => ({ ...prev, [keyName]: false }));
    }
  };

  const handleDeleteKey = async (service: ArtworkService, keyType: KeyType, keyId: string) => {
    if (!confirm('Are you sure you want to delete this API key?')) {
      return;
    }

    try {
      if (!keyId.startsWith('env_')) {
        await apiKeyStore.deleteServiceKey(service, keyType, keyId);
      }
      await loadServiceKeys();
      toast.success('API key deleted');
    } catch (error) {
      console.error('Failed to delete API key:', error);
      toast.error('Failed to delete API key');
    }
  };

  const toggleKeyVisibility = (service: ArtworkService, keyId: string) => {
    setServiceKeys(prev => ({
      ...prev,
      [service]: prev[service]?.map(key => 
        key.id === keyId ? { ...key, isVisible: !key.isVisible } : key
      ) || []
    }));
  };

  const testAPIKey = async (service: ArtworkService) => {
    try {
      const result = await artworkAPIRouter.routeRequest('search', { 
        gameName: 'Test Game',
        service 
      }, {
        preferredServices: [service]
      });

      if (result.success) {
        toast.success(`${service.toUpperCase()} API key is working!`);
      } else {
        toast.error(`${service.toUpperCase()} API test failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const renderServiceCard = (serviceInfo: ServiceInfo) => {
    const keys = serviceKeys[serviceInfo.service] || [];
    const keyName = `${serviceInfo.service}_${serviceInfo.keyType}`;
    const stats = statistics?.[serviceInfo.service];
    
    return (
      <Card key={serviceInfo.service} className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {serviceInfo.displayName}
                {serviceInfo.isRequired && (
                  <Badge variant="destructive" className="text-xs">Required</Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  {serviceInfo.costPerRequest > 0 ? `$${serviceInfo.costPerRequest}/req` : 'Free'}
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">{serviceInfo.description}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-2 h-2 rounded-full ${
                      i < serviceInfo.qualityRating ? 'bg-yellow-400' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
              <p className="text-xs text-gray-500">Quality Rating</p>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Features */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {serviceInfo.features.map(feature => (
                <Badge key={feature} variant="secondary" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>

          {/* Statistics */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{stats.totalRequests}</div>
                <div className="text-xs text-gray-600">Total Requests</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{stats.successfulRequests}</div>
                <div className="text-xs text-gray-600">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">{stats.averageResponseTime}ms</div>
                <div className="text-xs text-gray-600">Avg Response</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600">{stats.availableKeys}</div>
                <div className="text-xs text-gray-600">Available Keys</div>
              </div>
            </div>
          )}

          {/* Existing Keys */}
          {keys.length > 0 && (
            <div className="mb-4">
              <h4 className="font-medium mb-2">API Keys</h4>
              <div className="space-y-2">
                {keys.map(key => (
                  <div key={key.id} className="flex items-center gap-2 p-2 border rounded-lg">
                    <Key className="h-4 w-4 text-gray-500" />
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {key.label || `${serviceInfo.service.toUpperCase()} Key`}
                        {key.source === 'env' && (
                          <Badge variant="outline" className="ml-2 text-xs">Environment</Badge>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 font-mono">
                        {key.isVisible ? key.value : '••••••••••••••••'}
                      </div>
                      {key.usage && (
                        <div className="text-xs text-gray-500 mt-1">
                          {key.usage.totalRequests} requests • {key.usage.successfulRequests} successful
                          {key.usage.lastUsed > 0 && (
                            <> • Last used {new Date(key.usage.lastUsed).toLocaleDateString()}</>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleKeyVisibility(serviceInfo.service, key.id)}
                      >
                        {key.isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => testAPIKey(serviceInfo.service)}
                      >
                        <Zap className="h-4 w-4" />
                      </Button>
                      {key.source === 'user' && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteKey(serviceInfo.service, serviceInfo.keyType, key.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Add New Key */}
          <div className="space-y-3">
            <h4 className="font-medium">Add New {serviceInfo.keyLabel}</h4>
            <div className="space-y-2">
              <div>
                <Label htmlFor={`key-${keyName}`}>{serviceInfo.keyLabel}</Label>
                <Input
                  id={`key-${keyName}`}
                  type="password"
                  placeholder={`Enter your ${serviceInfo.keyLabel.toLowerCase()}`}
                  value={newKeyValues[keyName] || ''}
                  onChange={(e) => setNewKeyValues(prev => ({ 
                    ...prev, 
                    [keyName]: e.target.value 
                  }))}
                />
              </div>
              <div>
                <Label htmlFor={`label-${keyName}`}>Label (Optional)</Label>
                <Input
                  id={`label-${keyName}`}
                  placeholder={`My ${serviceInfo.displayName} Key`}
                  value={newKeyLabels[keyName] || ''}
                  onChange={(e) => setNewKeyLabels(prev => ({ 
                    ...prev, 
                    [keyName]: e.target.value 
                  }))}
                />
              </div>
              <Button
                onClick={() => handleAddKey(serviceInfo.service, serviceInfo.keyType)}
                disabled={saving[keyName] || !newKeyValues[keyName]?.trim()}
                className="w-full"
              >
                {saving[keyName] ? (
                  <>Loading...</>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add {serviceInfo.keyLabel}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Artwork API Management</h2>
        <p className="text-gray-600">
          Manage API keys for different artwork services. The system will intelligently 
          route requests to optimize for quality, cost, and rate limits.
        </p>
      </div>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          {servicesInfo.map(renderServiceCard)}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                System Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {queueStatus && (
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{queueStatus.queueLength}</div>
                    <div className="text-sm text-gray-600">Queued Requests</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{queueStatus.activeRequests}</div>
                    <div className="text-sm text-gray-600">Active Requests</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {queueStatus.processing ? 'Yes' : 'No'}
                    </div>
                    <div className="text-sm text-gray-600">Processing</div>
                  </div>
                </div>
              )}

              {statistics && (
                <div className="space-y-4">
                  <h3 className="font-medium">Service Performance</h3>
                  {Object.entries(statistics).map(([service, stats]) => (
                    <div key={service} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium capitalize">{service}</h4>
                        <Badge 
                          variant={stats.isCurrentlyLimited ? "destructive" : "secondary"}
                        >
                          {stats.isCurrentlyLimited ? 'Rate Limited' : 'Available'}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                        <div>
                          <div className="font-medium text-blue-600">{stats.totalRequests}</div>
                          <div className="text-gray-600">Total Requests</div>
                        </div>
                        <div>
                          <div className="font-medium text-green-600">{stats.successfulRequests}</div>
                          <div className="text-gray-600">Successful</div>
                        </div>
                        <div>
                          <div className="font-medium text-purple-600">{stats.averageResponseTime}ms</div>
                          <div className="text-gray-600">Avg Response</div>
                        </div>
                        <div>
                          <div className="font-medium text-orange-600">{stats.availableKeys}</div>
                          <div className="text-gray-600">Available Keys</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Router Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Use Environment Keys as Fallback</Label>
                  <p className="text-sm text-gray-600">
                    Allow using environment variables when user keys are not available
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable High Quality Mode</Label>
                  <p className="text-sm text-gray-600">
                    Prefer services with higher quality ratings (may increase cost)
                  </p>
                </div>
                <Switch />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable Cost Optimization</Label>
                  <p className="text-sm text-gray-600">
                    Prefer free or low-cost services when possible
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="pt-4 border-t">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    rateLimitManager.cleanup();
                    toast.success('Rate limit cache cleared');
                  }}
                >
                  Clear Rate Limit Cache
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ArtworkAPIKeyManager;