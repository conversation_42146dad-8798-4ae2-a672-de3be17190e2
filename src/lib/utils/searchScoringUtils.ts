import { Game } from '@/types';

export interface SearchScore {
  game: Game;
  score: number;
  breakdown: {
    titleSimilarity: number;
    platformRelevance: number;
    recencyScore: number;
    ratingScore: number;
    totalScore: number;
  };
}

export interface ScoringWeights {
  titleSimilarity: number;
  platformRelevance: number;
  recencyScore: number;
  ratingScore: number;
}

// Default weights for balanced scoring
const DEFAULT_WEIGHTS: ScoringWeights = {
  titleSimilarity: 0.5,    // 50% - Most important
  platformRelevance: 0.2,  // 20% - Platform preference
  recencyScore: 0.15,      // 15% - Newer games slight boost
  ratingScore: 0.15        // 15% - Quality preference
};

/**
 * Calculate Levenshtein distance between two strings
 * Used for fuzzy string matching
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix: number[][] = [];
  
  // Initialize matrix
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  // Fill matrix
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Calculate title similarity score between search query and game title
 */
function calculateTitleSimilarity(searchQuery: string, gameTitle: string): number {
  const query = searchQuery.toLowerCase().trim();
  const title = gameTitle.toLowerCase().trim();
  
  // Exact match gets perfect score
  if (query === title) {
    return 1.0;
  }
  
  // Check if query is contained in title
  if (title.includes(query)) {
    // Score based on how much of the title matches
    return Math.min(1.0, query.length / title.length + 0.3);
  }
  
  // Check if title is contained in query (for longer queries)
  if (query.includes(title)) {
    return Math.min(1.0, title.length / query.length + 0.2);
  }
  
  // Fuzzy matching using Levenshtein distance
  const distance = levenshteinDistance(query, title);
  const maxLength = Math.max(query.length, title.length);
  const similarity = Math.max(0, 1 - distance / maxLength);
  
  // Boost score for partial word matches
  const queryWords = query.split(' ').filter(w => w.length > 2);
  const titleWords = title.split(' ').filter(w => w.length > 2);
  
  let wordMatchBonus = 0;
  queryWords.forEach(queryWord => {
    titleWords.forEach(titleWord => {
      if (titleWord.includes(queryWord) || queryWord.includes(titleWord)) {
        wordMatchBonus += 0.1;
      }
    });
  });
  
  return Math.min(1.0, similarity + wordMatchBonus);
}

/**
 * Calculate platform relevance score based on user preferences
 */
function calculatePlatformRelevance(
  game: Game, 
  preferredPlatforms: string[] = [],
  searchedPlatforms: string[] = []
): number {
  if (!game.platforms || game.platforms.length === 0) {
    return 0.5; // Neutral score for games without platform info
  }
  
  let maxRelevance = 0;
  
  // Check against searched platforms (highest priority)
  if (searchedPlatforms.length > 0) {
    game.platforms.forEach(platform => {
      searchedPlatforms.forEach(searchedPlatform => {
        if (platform.toLowerCase().includes(searchedPlatform.toLowerCase()) ||
            searchedPlatform.toLowerCase().includes(platform.toLowerCase())) {
          maxRelevance = Math.max(maxRelevance, 1.0);
        }
      });
    });
  }
  
  // Check against user preferred platforms
  if (preferredPlatforms.length > 0 && maxRelevance < 1.0) {
    game.platforms.forEach(platform => {
      preferredPlatforms.forEach(preferredPlatform => {
        if (platform.toLowerCase().includes(preferredPlatform.toLowerCase()) ||
            preferredPlatform.toLowerCase().includes(platform.toLowerCase())) {
          maxRelevance = Math.max(maxRelevance, 0.8);
        }
      });
    });
  }
  
  // Default score for games with platform info but no preference match
  return maxRelevance > 0 ? maxRelevance : 0.6;
}

/**
 * Calculate recency score based on release date
 */
function calculateRecencyScore(game: Game): number {
  if (!game.release_date) {
    return 0.5; // Neutral score for games without release date
  }
  
  try {
    const releaseDate = new Date(game.release_date);
    const now = new Date();
    const currentYear = now.getFullYear();
    const releaseYear = releaseDate.getFullYear();
    
    // Games from current year get highest score
    if (releaseYear === currentYear) {
      return 1.0;
    }
    
    // Calculate score based on age (newer games get higher scores)
    const yearsDiff = currentYear - releaseYear;
    
    if (yearsDiff <= 1) return 0.9;      // Last year
    if (yearsDiff <= 2) return 0.8;      // 2 years ago
    if (yearsDiff <= 5) return 0.7;      // 5 years ago
    if (yearsDiff <= 10) return 0.6;     // 10 years ago
    if (yearsDiff <= 15) return 0.5;     // 15 years ago
    if (yearsDiff <= 20) return 0.4;     // 20 years ago
    
    return 0.3; // Retro games (20+ years old)
    
  } catch {
    console.warn('Error parsing release date:', game.release_date);
    return 0.5;
  }
}

/**
 * Calculate rating score based on Metacritic score
 */
function calculateRatingScore(game: Game): number {
  if (!game.metacritic_score || game.metacritic_score <= 0) {
    return 0.5; // Neutral score for games without ratings
  }
  
  const score = game.metacritic_score;
  
  // Convert Metacritic score (0-100) to 0-1 scale with curve
  if (score >= 90) return 1.0;      // Exceptional
  if (score >= 80) return 0.9;      // Great
  if (score >= 70) return 0.8;      // Good
  if (score >= 60) return 0.7;      // Decent
  if (score >= 50) return 0.6;      // Average
  if (score >= 40) return 0.5;      // Below average
  if (score >= 30) return 0.4;      // Poor
  
  return 0.3; // Very poor
}

/**
 * Score a single game against search criteria
 */
export function scoreGame(
  game: Game,
  searchQuery: string,
  options: {
    preferredPlatforms?: string[];
    searchedPlatforms?: string[];
    weights?: Partial<ScoringWeights>;
  } = {}
): SearchScore {
  const weights = { ...DEFAULT_WEIGHTS, ...options.weights };
  
  // Calculate individual scores
  const titleSimilarity = calculateTitleSimilarity(searchQuery, game.title);
  const platformRelevance = calculatePlatformRelevance(
    game, 
    options.preferredPlatforms, 
    options.searchedPlatforms
  );
  const recencyScore = calculateRecencyScore(game);
  const ratingScore = calculateRatingScore(game);
  
  // Calculate weighted total score
  const totalScore = 
    titleSimilarity * weights.titleSimilarity +
    platformRelevance * weights.platformRelevance +
    recencyScore * weights.recencyScore +
    ratingScore * weights.ratingScore;
  
  return {
    game,
    score: totalScore,
    breakdown: {
      titleSimilarity: Math.round(titleSimilarity * 100) / 100,
      platformRelevance: Math.round(platformRelevance * 100) / 100,
      recencyScore: Math.round(recencyScore * 100) / 100,
      ratingScore: Math.round(ratingScore * 100) / 100,
      totalScore: Math.round(totalScore * 100) / 100
    }
  };
}

/**
 * Score and sort an array of games
 */
export function scoreAndSortGames(
  games: Game[],
  searchQuery: string,
  options: {
    preferredPlatforms?: string[];
    searchedPlatforms?: string[];
    weights?: Partial<ScoringWeights>;
    limit?: number;
    minScore?: number;
  } = {}
): SearchScore[] {
  const { limit, minScore = 0.1 } = options;
  
  // Score all games
  const scoredGames = games
    .map(game => scoreGame(game, searchQuery, options))
    .filter(score => score.score >= minScore)
    .sort((a, b) => b.score - a.score);
  
  // Apply limit if specified
  return limit ? scoredGames.slice(0, limit) : scoredGames;
}

/**
 * Extract just the games from scored results
 */
export function extractGamesFromScores(scoredGames: SearchScore[]): Game[] {
  return scoredGames.map(score => score.game);
}

/**
 * Debug function to log scoring breakdown
 */
export function debugScoring(scoredGames: SearchScore[], searchQuery: string): void {
  console.group(`🎯 Search Scoring Debug for "${searchQuery}"`);
  console.log(`📊 Scored ${scoredGames.length} games`);
  
  scoredGames.slice(0, 5).forEach((score, index) => {
    console.log(`\n${index + 1}. "${score.game.title}" (Score: ${score.breakdown.totalScore})`);
    console.log(`   • Title Similarity: ${score.breakdown.titleSimilarity}`);
    console.log(`   • Platform Relevance: ${score.breakdown.platformRelevance}`);
    console.log(`   • Recency: ${score.breakdown.recencyScore}`);
    console.log(`   • Rating: ${score.breakdown.ratingScore}`);
  });
  
  console.groupEnd();
}