import { gameAPI } from './api/games';
import { Game } from '@/types';
import { 
  cleanQuery,
  isLikelyGameName,
  containsMoodKeywords,
  extractMood,
  containsGenreKeywords,
  extractGenre,
  containsPlatformKeywords,
  extractPlatform,
  extractYearRange,
  extractRatingRange,
  extractSimilarGameQuery,
  isRecommendationQuery,
  generateSearchSuggestions
} from './utils/searchUtils';
import { generateUUID } from './utils/baseUtils';
import { safeJsonParse, safeJsonStringify } from './utils/baseUtils';
import { searchCacheService } from './services/searchCacheService';

// Type for IGDB API search results
type APISearchResult = Game[];

interface SearchIntent {
  type: 'basic' | 'mood' | 'genre' | 'platform' | 'year' | 'rating' | 'playtime' | 'similar' | 'recommendation';
  confidence: number;
  extractedTerms: {
    games?: string[];
    genres?: string[];
    platforms?: string[];
    moods?: string[];
    yearRange?: { from?: number; to?: number };
    rating?: { min?: number; max?: number };
    playtime?: { min?: number; max?: number };
    keywords?: string[];
  };
  originalQuery: string;
  processedQuery: string;
}

interface SearchResult {
  games: Game[];
  intent: SearchIntent;
  suggestions: string[];
  filters: {
    genres: string[];
    platforms: string[];
    yearRange?: { from: number; to: number };
    ratingRange?: { min: number; max: number };
  };
  searchTime: number;
}

interface SearchHistory {
  id: string;
  query: string;
  intent: SearchIntent;
  timestamp: string;
  resultCount: number;
  userId: string;
}

class EnhancedSearchService {
  private readonly GEMINI_API_KEY?: string;
  private readonly OPENAI_API_KEY?: string;
  private readonly DEEPSEEK_API_KEY?: string;
  private readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
  private readonly OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
  private readonly DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
  private readonly hasAI: boolean;
  private readonly aiProvider: 'gemini' | 'openai' | 'deepseek' | null;

  constructor() {
    this.GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
    this.OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
    this.DEEPSEEK_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY;
    
    // Determine which AI provider to use (priority: Gemini > OpenAI > DeepSeek)
    if (this.GEMINI_API_KEY) {
      this.aiProvider = 'gemini';
      this.hasAI = true;
      console.log('🤖 Enhanced search using Gemini AI');
    } else if (this.OPENAI_API_KEY) {
      this.aiProvider = 'openai';
      this.hasAI = true;
      console.log('🤖 Enhanced search using OpenAI');
    } else if (this.DEEPSEEK_API_KEY) {
      this.aiProvider = 'deepseek';
      this.hasAI = true;
      console.log('🤖 Enhanced search using DeepSeek AI');
    } else {
      this.aiProvider = null;
      this.hasAI = false;
      console.warn('No AI API keys found - AI features will be disabled, using pattern-based search only');
    }
  }

  /**
   * Enhanced search with natural language processing
   */
  async enhancedSearch(query: string, userId?: string, useCache: boolean = true): Promise<SearchResult> {
    const startTime = Date.now();

    try {
      // Check cache first if enabled
      if (useCache) {
        const cachedResult = await searchCacheService.getCachedResult(query, userId);
        if (cachedResult) {
          console.log(`🚀 Cache HIT for query: "${query}" - Response time: ${Date.now() - startTime}ms`);
          return {
            ...cachedResult,
            searchTime: Date.now() - startTime // Update search time to include cache retrieval
          };
        }
      }

      // Parse the search intent
      const intent = await this.parseSearchIntent(query);
      
      // Perform the search based on intent
      let searchResults: Game[] = [];
      let suggestions: string[] = [];

      switch (intent.type) {
        case 'mood':
          searchResults = await this.searchByMood(intent);
          suggestions = this.generateMoodSuggestions(intent);
          break;
        case 'genre':
          searchResults = await this.searchByGenre(intent);
          suggestions = this.generateGenreSuggestions();
          break;
        case 'platform':
          searchResults = await this.searchByPlatform(intent);
          suggestions = this.generatePlatformSuggestions();
          break;
        case 'year':
          searchResults = await this.searchByYear(intent);
          suggestions = this.generateYearSuggestions();
          break;
        case 'similar':
          searchResults = await this.searchSimilarGames(intent);
          suggestions = this.generateSimilarSuggestions();
          break;
        case 'recommendation':
          searchResults = await this.getRecommendations(intent, userId);
          suggestions = this.generateRecommendationSuggestions();
          break;
        default:
          searchResults = await this.basicSearch(intent.processedQuery);
          suggestions = this.generateBasicSuggestions();
      }

      // Ensure games is a flat array
      const flatGameList = searchResults;

      // Generate filters based on results
      const filters = this.generateFilters(flatGameList);

      // Save search history
      if (userId) {
        await this.saveSearchHistory(query, intent, flatGameList.length, userId);
      }

      const searchTime = Date.now() - startTime;

      const result: SearchResult = {
        games: flatGameList,
        intent,
        suggestions,
        filters,
        searchTime
      };

      // Cache the result if caching is enabled and we have results
      if (useCache && flatGameList.length > 0) {
        try {
          await searchCacheService.cacheResult(query, intent, result, userId);
          console.log(`💾 Cached search result for query: "${query}" with ${flatGameList.length} games`);
        } catch (error) {
          console.warn('Failed to cache search result:', error);
        }
      }

      return result;

    } catch (error) {
      console.error('Enhanced search error:', error);
      
      // Fallback to basic search
      const searchResults = await this.basicSearch(query);
      const games = searchResults;
      const searchTime = Date.now() - startTime;

      return {
        games,
        intent: {
          type: 'basic',
          confidence: 0.5,
          extractedTerms: { keywords: [query] },
          originalQuery: query,
          processedQuery: query
        },
        suggestions: this.generateBasicSuggestions(),
        filters: this.generateFilters(games),
        searchTime
      };
    }
  }

  /**
   * Parse search intent using AI and pattern matching
   */
  private async parseSearchIntent(query: string): Promise<SearchIntent> {
    try {
      // First, try pattern-based parsing for speed
      const patternIntent = this.parseWithPatterns(query);
      
      // If AI is not available, use pattern-based parsing only
      if (!this.hasAI) {
        console.log('📋 Using pattern-based intent parsing (AI not available)');
        return patternIntent;
      }
      
      // Use higher confidence threshold to avoid AI calls for most queries
      if (patternIntent.confidence > 0.75) {
        console.log('✅ Using pattern-based intent parsing (avoiding AI call)');
        return patternIntent;
      }

      // Only use AI for truly complex queries that patterns can't handle
      if (patternIntent.confidence < 0.4) {
        console.log('🤖 Using AI-based intent parsing for complex query');
        const aiIntent = await this.parseWithAI(query);
        return aiIntent.confidence > patternIntent.confidence ? aiIntent : patternIntent;
      }

      // Use pattern result for medium confidence queries
      console.log('📋 Using pattern-based intent parsing (medium confidence)');
      return patternIntent;

    } catch (error) {
      console.error('Intent parsing error:', error);
      return this.parseWithPatterns(query);
    }
  }

  /**
   * Pattern-based intent parsing for common queries
   */
  private parseWithPatterns(query: string): SearchIntent {
    const lowercaseQuery = query.toLowerCase();
    const extractedTerms: SearchIntent['extractedTerms'] = {};
    let type: SearchIntent['type'] = 'basic';
    let confidence = 0.5;

    // Enhanced pattern matching for common query types
    
    // 1. Direct game name queries (highest confidence)
    if (isLikelyGameName(query)) {
      type = 'basic';
      confidence = 0.95;
      extractedTerms.keywords = [query.trim()];
    }
    
    // 2. Check for mood-based queries
    else if (containsMoodKeywords(lowercaseQuery)) {
      const mood = extractMood(lowercaseQuery);
      if (mood) {
        type = 'mood';
        extractedTerms.moods = [mood];
        confidence = 0.9;
      }
    }

    // 3. Check for genre queries
    else if (containsGenreKeywords(lowercaseQuery)) {
      const genre = extractGenre(lowercaseQuery);
      if (genre) {
        type = 'genre';
        extractedTerms.genres = [genre];
        confidence = 0.9;
      }
    }

    // 4. Check for platform queries
    else if (containsPlatformKeywords(lowercaseQuery)) {
      const platform = extractPlatform(lowercaseQuery);
      if (platform) {
        type = 'platform';
        extractedTerms.platforms = [platform];
        confidence = 0.9;
      }
    }

    // 5. Check for year ranges
    const yearMatch = extractYearRange(lowercaseQuery);
    if (yearMatch) {
      extractedTerms.yearRange = yearMatch;
      if (type === 'basic') {
        type = 'year';
        confidence = 0.85;
      }
    }

    // 6. Check for rating queries
    const ratingMatch = extractRatingRange(lowercaseQuery);
    if (ratingMatch) {
      extractedTerms.rating = ratingMatch;
      if (type === 'basic') {
        type = 'rating';
        confidence = 0.8;
      }
    }

    // 7. Check for similar game queries
    const similarMatch = extractSimilarGameQuery(lowercaseQuery);
    if (similarMatch) {
      type = 'similar';
      extractedTerms.games = [similarMatch];
      confidence = 0.85;
    }

    // 8. Check for recommendation queries
    if (isRecommendationQuery(lowercaseQuery)) {
      type = 'recommendation';
      confidence = 0.8;
    }

    // 9. Boost confidence for simple, clear queries
    if (query.trim().split(' ').length <= 2 && type === 'basic') {
      confidence = Math.max(confidence, 0.8);
    }

    return {
      type,
      confidence,
      extractedTerms,
      originalQuery: query,
      processedQuery: cleanQuery(query)
    };
  }

  /**
   * AI-powered intent parsing using available AI provider
   */
  private async parseWithAI(query: string): Promise<SearchIntent> {
    if (!this.hasAI || !this.aiProvider) {
      console.warn('AI parsing requested but no API key available, falling back to patterns');
      return this.parseWithPatterns(query);
    }
    
    try {
      const prompt = `
Analyze this game search query and extract the intent and key information:

Query: "${query}"

Return a JSON object with:
{
  "type": "basic|mood|genre|platform|year|rating|playtime|similar|recommendation",
  "confidence": 0.0-1.0,
  "extractedTerms": {
    "games": ["game names if mentioned"],
    "genres": ["extracted genres"],
    "platforms": ["extracted platforms"],
    "moods": ["relaxing|challenging|story-driven|multiplayer|creative"],
    "yearRange": {"from": year, "to": year},
    "rating": {"min": 0-10, "max": 0-10},
    "keywords": ["other important terms"]
  },
  "processedQuery": "cleaned query for search"
}

Examples:
- "relaxing puzzle games" -> type: "mood", moods: ["relaxing"], genres: ["puzzle"]
- "games like Skyrim" -> type: "similar", games: ["Skyrim"]
- "best RPGs from 2020" -> type: "genre", genres: ["rpg"], yearRange: {"from": 2020, "to": 2020}

Return only the JSON object, no additional text.
`;

      let response: Response;
      
      switch (this.aiProvider) {
        case 'gemini':
          response = await fetch(`${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              contents: [{ parts: [{ text: prompt }] }],
              generationConfig: {
                temperature: 0.3,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1024,
              }
            })
          });
          break;
          
        case 'openai':
          response = await fetch(this.OPENAI_API_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.OPENAI_API_KEY}`
            },
            body: JSON.stringify({
              model: 'gpt-3.5-turbo',
              messages: [
                {
                  role: 'user',
                  content: prompt
                }
              ],
              temperature: 0.3,
              max_tokens: 1024
            })
          });
          break;
          
        case 'deepseek':
          response = await fetch(this.DEEPSEEK_API_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.DEEPSEEK_API_KEY}`
            },
            body: JSON.stringify({
              model: 'deepseek-chat',
              messages: [
                {
                  role: 'user',
                  content: prompt
                }
              ],
              temperature: 0.3,
              max_tokens: 1024
            })
          });
          break;
          
        default:
          throw new Error('No AI provider configured');
      }

      if (!response.ok) {
        throw new Error(`${this.aiProvider} API error: ${response.status}`);
      }

      const data = await response.json();
      let aiResponse: string;
      
      switch (this.aiProvider) {
        case 'gemini':
          aiResponse = data.candidates?.[0]?.content?.parts?.[0]?.text;
          break;
        case 'openai':
        case 'deepseek':
          aiResponse = data.choices?.[0]?.message?.content;
          break;
        default:
          throw new Error('Unknown AI provider');
      }
      
      if (!aiResponse) {
        throw new Error(`No response from ${this.aiProvider} API`);
      }

      const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanResponse);

      return {
        type: parsed.type || 'basic',
        confidence: Math.min(Math.max(parsed.confidence || 0.5, 0), 1),
        extractedTerms: parsed.extractedTerms || {},
        originalQuery: query,
        processedQuery: parsed.processedQuery || cleanQuery(query)
      };

    } catch (error) {
      console.error(`${this.aiProvider} intent parsing error:`, error);
      return this.parseWithPatterns(query);
    }
  }



  /**
   * Search implementations for different intent types
   */
  private async searchByMood(intent: SearchIntent): Promise<Game[]> {
    const mood = intent.extractedTerms.moods?.[0];
    if (!mood) return [];

    // Mock implementation - in real app, this would query based on mood tags
    const moodGenres = {
      relaxing: ['puzzle', 'simulation', 'indie'],
      challenging: ['action', 'strategy', 'fighting'],
      'story-driven': ['rpg', 'adventure'],
      multiplayer: ['action', 'sports', 'strategy'],
      creative: ['simulation', 'indie', 'sandbox']
    };

    const genres = moodGenres[mood as keyof typeof moodGenres] || [];
    return this.searchByGenreList(genres);
  }

  private async searchByGenre(intent: SearchIntent): Promise<Game[]> {
    const genres = intent.extractedTerms.genres || [];
    return this.searchByGenreList(genres);
  }

  private async searchByGenreList(genres: string[]): Promise<Game[]> {
    if (genres.length === 0) return [];

    try {
      // Use IGDB API to search games by genre
      const searchPromises = genres.map(genre => 
        gameAPI.search(`${genre} games`, { limit: 10, genres: [genre] })
      );
      
      const results = await Promise.all(searchPromises);
      const allGames: Game[] = [];
      
      results.forEach(igdbGames => {
        const games = igdbGames.map(g => gameAPI.convertToGame(g));
        allGames.push(...games);
      });
      
      return allGames;
    } catch (error) {
      console.error('Genre search error:', error);
      return [];
    }
  }

  private async searchByPlatform(intent: SearchIntent): Promise<Game[]> {
    const platforms = intent.extractedTerms.platforms || [];
    if (platforms.length === 0) return [];

    // This would filter games by platform in a real implementation
    const result = await this.basicSearch(intent.processedQuery);
    return result;
  }

  private async searchByYear(intent: SearchIntent): Promise<Game[]> {
    const yearRange = intent.extractedTerms.yearRange;
    if (!yearRange) return [];

    // This would filter by release year in a real implementation
    const result = await this.basicSearch(intent.processedQuery);
    return result;
  }

  private async searchSimilarGames(intent: SearchIntent): Promise<Game[]> {
    const games = intent.extractedTerms.games || [];
    if (games.length === 0) return [];

    const searchTerm = games[0];
    const result = await this.basicSearch(searchTerm);
    return result;
  }

  private async getRecommendations(intent: SearchIntent, userId?: string): Promise<Game[]> {
    let result: APISearchResult;
    
    if (!userId) {
      // Return popular games if no user context
      result = await this.basicSearch('popular games');
    } else {
      // This would use the AI recommendation service from Phase 1
      result = await this.basicSearch(intent.processedQuery);
    }
    
    return result;
  }

  private async basicSearch(query: string): Promise<APISearchResult> {
    if (!query.trim()) return [];
    
    try {
      const igdbGames = await gameAPI.search(query, { limit: 100 });
      return igdbGames.map(g => gameAPI.convertToGame(g));
    } catch (error) {
      console.error('Basic search error:', error);
      return [];
    }
  }

  /**
   * Generate suggestions based on search intent
   */
  private generateMoodSuggestions(intent: SearchIntent): string[] {
    const currentMood = intent.extractedTerms.moods?.[0];
    const suggestions = [
      'relaxing puzzle games',
      'challenging action games',
      'story-driven RPGs',
      'multiplayer co-op games',
      'creative sandbox games'
    ];
    
    return suggestions.filter(s => !s.includes(currentMood || ''));
  }

  private generateGenreSuggestions(): string[] {
    return [
      'indie action games',
      'turn-based strategy',
      'open-world RPGs',
      'puzzle platformers',
      'racing simulations'
    ];
  }

  private generatePlatformSuggestions(): string[] {
    return [
      'Steam exclusive games',
      'PlayStation exclusives',
      'Nintendo Switch games',
      'cross-platform multiplayer',
      'mobile-friendly games'
    ];
  }

  private generateYearSuggestions(): string[] {
    const currentYear = new Date().getFullYear();
    return [
      `best games of ${currentYear}`,
      `retro games from the 90s`,
      `indie games from 2020`,
      `upcoming releases ${currentYear + 1}`,
      'classic games'
    ];
  }

  private generateSimilarSuggestions(): string[] {
    return [
      'games like Witcher 3',
      'similar to Stardew Valley',
      'games like Dark Souls',
      'similar to Minecraft',
      'games like Among Us'
    ];
  }

  private generateRecommendationSuggestions(): string[] {
    return [
      'recommend RPGs',
      'suggest indie games',
      'what should I play next',
      'games for beginners',
      'hidden gems'
    ];
  }

  private generateBasicSuggestions(): string[] {
    return generateSearchSuggestions('', 5);
  }

  /**
   * Generate filters based on search results
   */
  private generateFilters(games: Game[] | APISearchResult): SearchResult['filters'] {
    const genres = new Set<string>();
    const platforms = new Set<string>();
    let minYear = Infinity;
    let maxYear = -Infinity;
    let minRating = Infinity;
    let maxRating = -Infinity;

    let gameList: Game[] = [];
    if (Array.isArray(games)) {
      gameList = games;
    } else if (games && typeof games === 'object' && 'igdb' in games && 'rawg' in games) {
      gameList = games;
    }

    gameList.forEach(game => {
      if (!game) return;

      // Extract genres
      if (game.genres) {
        game.genres.forEach(genre => genres.add(genre));
      }

      // Extract platforms
      if (game.platforms) {
        game.platforms.forEach(platform => platforms.add(platform));
      }

      // Extract years
      if (game.release_date) {
        const year = new Date(game.release_date).getFullYear();
        if (!isNaN(year) && year > 1900 && year <= new Date().getFullYear() + 2) {
          minYear = Math.min(minYear, year);
          maxYear = Math.max(maxYear, year);
        }
      }

      // Extract ratings
      if (game.metacritic_score) {
        const rating = game.metacritic_score / 10; // Convert to 0-10 scale
        if (rating > 0) {
          minRating = Math.min(minRating, rating);
          maxRating = Math.max(maxRating, rating);
        }
      }
    });

    return {
      genres: Array.from(genres).slice(0, 10),
      platforms: Array.from(platforms).slice(0, 8),
      yearRange: minYear !== Infinity ? { from: minYear, to: maxYear } : undefined,
      ratingRange: minRating !== Infinity ? { min: Math.floor(minRating), max: Math.ceil(maxRating) } : undefined
    };
  }



  /**
   * Save search to history
   */
  private async saveSearchHistory(
    query: string,
    intent: SearchIntent,
    resultCount: number,
    userId: string
  ): Promise<void> {
    try {
      const historyEntry = {
        id: generateUUID(),
        query,
        intent,
        timestamp: new Date().toISOString(),
        resultCount,
        userId
      };

      // In a real implementation, this would save to the database
      const existingHistory = localStorage.getItem(`search_history_${userId}`);
      const history: SearchHistory[] = safeJsonParse(existingHistory || '[]', []);
      
      history.unshift(historyEntry);
      
      // Keep only last 50 searches
      const trimmedHistory = history.slice(0, 50);
      localStorage.setItem(`search_history_${userId}`, safeJsonStringify(trimmedHistory));

    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }

  /**
   * Get search history for user
   */
  getSearchHistory(userId: string): SearchHistory[] {
    try {
      const history = localStorage.getItem(`search_history_${userId}`);
      return safeJsonParse(history || '[]', []);
    } catch (error) {
      console.error('Error getting search history:', error);
      return [];
    }
  }

  /**
   * Get popular searches
   */
  getPopularSearches(): string[] {
    return generateSearchSuggestions('popular', 10);
  }

  /**
   * Voice search processing
   */
  async processVoiceSearch(transcript: string): Promise<SearchResult> {
    // Clean up voice transcript
    const cleanedQuery = cleanQuery(transcript
      .replace(/\b(um|uh|like|you know)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim());

    return this.enhancedSearch(cleanedQuery);
  }
}

export const enhancedSearchService = new EnhancedSearchService();
export type { SearchIntent, SearchResult, SearchHistory };