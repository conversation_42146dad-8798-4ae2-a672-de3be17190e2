import { supabase } from './supabase';
import { 
  Game, 
  Platform,
  TheGamesDBGame,
  TheGamesDBImage,
  TheGamesDBImageBaseUrl,
  TheGamesDBPlatform,
  TheGamesDBGenre,
  TheGamesDBDeveloper,
  TheGamesDBPublisher,
  TheGamesDBSearchOptions
} from '../types';

// IGDB Game interface matching API response
interface IGDBGame {
  id: number;
  name: string;
  platforms?: Array<{ id: number; name: string }>;
  genres?: Array<{ id: number; name: string }>;
  involved_companies?: Array<{
    company: { id: number; name: string };
    developer: boolean;
    publisher: boolean;
  }>;
  first_release_date?: number;
  summary?: string;
  cover?: { url: string; image_id: string };
  screenshots?: Array<{ url: string; image_id: string }>;
  aggregated_rating?: number;
  videos?: Array<{ video_id: string; name: string }>;
  similar_games?: Array<{ id: number; name: string }>;
  category?: number; // 0 = main game, 1 = DLC, 2 = expansion
  themes?: Array<{ id: number; name: string }>;
  keywords?: Array<{ id: number; name: string }>;
  version_parent?: number | null; // Parent game ID if this is a version
  version_title?: string; // Title of the specific version
}

// Search options interface
interface IGDBSearchOptions {
  limit?: number;
  platforms?: string[];
  genres?: string[];
  minRating?: number;
  maxRating?: number;
  minYear?: number;
  maxYear?: number;
  sortBy?: 'relevance' | 'rating' | 'release_date' | 'name' | 'popularity';
  sortDirection?: 'asc' | 'desc';
  excludeDLC?: boolean;
  excludeExpansions?: boolean;
  searchType?: 'exact' | 'fuzzy' | 'smart';
}

// YouTube interfaces
interface YouTubeVideo {
  id: { videoId: string };
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      default: { url: string };
      medium: { url: string };
      high: { url: string };
    };
  };
}

interface YouTubeSearchResponse {
  items: YouTubeVideo[];
}

class TheGamesDBAPIService {
  private readonly baseUrl = 'https://api.thegamesdb.net';
  private readonly apiKey: string | null;
  private platformMap: Record<number, Platform> = {};
  private genreMap: Record<number, string> = {};
  private developerMap: Record<number, string> = {};
  private publisherMap: Record<number, string> = {};

  constructor() {
    this.apiKey = import.meta.env.VITE_THEGAMESDB_API_KEY;
    console.log('🎮 TheGamesDB API Service initialized');
    this.initializeMappings();
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_thegamesdb_api_key');
  }

  private async initializeMappings() {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return;
    }

    try {
      await Promise.all([
        this.loadPlatformMapping(),
        this.loadGenreMapping(),
        this.loadDeveloperMapping(),
        this.loadPublisherMapping()
      ]);
      console.log('✅ TheGamesDB mappings initialized');
    } catch (error) {
      console.warn('⚠️ Failed to initialize TheGamesDB mappings:', error);
    }
  }

  private async makeRequest<T>(endpoint: string, params: Record<string, string | number | string[]> = {}): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error('TheGamesDB API key not configured');
    }

    try {
      console.log(`🔍 TheGamesDB request via proxy: ${endpoint}`);
      console.log(`🔍 TheGamesDB params:`, params);

      const { data, error } = await supabase.functions.invoke('thegamesdb-proxy', {
        headers: {
          'X-API-Key': this.apiKey!,
        },
        body: { endpoint, params },
      });

      if (error) {
        console.error('❌ TheGamesDB proxy error:', error);
        throw new Error(`TheGamesDB proxy error: ${error.message}`);
      }

      if (data?.error) {
        console.error('❌ TheGamesDB response error:', data.error);
        throw new Error(`TheGamesDB error: ${data.error}`);
      }

      if (!data) {
        throw new Error('No data received from TheGamesDB API');
      }

      console.log(`✅ TheGamesDB request successful - Monthly allowance remaining: ${data.remaining_monthly_allowance}`);
      return data.data;
    } catch (error) {
      console.error('❌ TheGamesDB API error:', error);
      throw error;
    }
  }

  private async loadPlatformMapping() {
    try {
      await this.makeRequest<{ count: number; platforms: Record<string, TheGamesDBPlatform> }>('/v1/Platforms');
      
      // More comprehensive TheGamesDB platform mapping
      const mapping: Record<number, Platform> = {
        1: 'PC', // PC
        4: 'PC', // PC
        3: 'Nintendo 64', // Nintendo 64
        7: 'Nintendo Switch', // Nintendo Switch
        8: 'PlayStation 4', // PlayStation 4
        9: 'PlayStation 3', // PlayStation 3
        10: 'PlayStation 2', // PlayStation 2
        11: 'PlayStation', // PlayStation
        12: 'Xbox', // Xbox
        13: 'PC', // PC
        14: 'Xbox 360', // Xbox 360
        15: 'Xbox One', // Xbox One
        16: 'Steam Deck', // Steam Deck
        18: 'Nintendo 3DS', // Nintendo 3DS
        20: 'Nintendo DS', // Nintendo DS
        21: 'GameCube', // GameCube
        23: 'Wii', // Wii
        25: '3DO', // 3DO
        26: 'iOS', // iOS
        27: 'Android', // Android
        38: 'Wii U', // Wii U
        39: 'iOS', // iOS
        4916: 'PlayStation 5', // PlayStation 5
        4920: 'Xbox Series X/S', // Xbox Series X/S
        4950: 'Xbox Series X/S', // Xbox Series X/S (alt)
      } as Record<number, Platform>;

      this.platformMap = mapping;
    } catch (error) {
      console.warn('⚠️ Failed to load platform mapping:', error);
    }
  }

  private async loadGenreMapping() {
    try {
      const data = await this.makeRequest<{ count: number; genres: Record<string, TheGamesDBGenre> }>('/v1/Genres');
      
      this.genreMap = Object.values(data.genres).reduce((acc, genre) => {
        acc[genre.id] = genre.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load genre mapping:', error);
    }
  }

  private async loadDeveloperMapping() {
    try {
      const data = await this.makeRequest<{ count: number; developers: Record<string, TheGamesDBDeveloper> }>('/v1/Developers');
      
      this.developerMap = Object.values(data.developers).reduce((acc, dev) => {
        acc[dev.id] = dev.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load developer mapping:', error);
    }
  }

  private async loadPublisherMapping() {
    try {
      const data = await this.makeRequest<{ count: number; publishers: Record<string, TheGamesDBPublisher> }>('/v1/Publishers');
      
      this.publisherMap = Object.values(data.publishers).reduce((acc, pub) => {
        acc[pub.id] = pub.name;
        return acc;
      }, {} as Record<number, string>);
    } catch (error) {
      console.warn('⚠️ Failed to load publisher mapping:', error);
    }
  }

  async searchByName(name: string, options: TheGamesDBSearchOptions = {}): Promise<TheGamesDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return [];
    }

    const {
      platforms = [],
      fields = ['players', 'publishers', 'genres', 'overview', 'rating', 'platform', 'coop', 'youtube'],
      include = ['boxart', 'platform'],
      page = 1
    } = options;

    try {
      const params: Record<string, string | number | string[]> = {
        name,
        fields: fields.join(','),
        include: include.join(','),
        page
      };

      // Implement proper platform filtering using TheGamesDB format
      if (platforms.length > 0) {
        console.log(`🎯 TheGamesDB API: Applying platform filters to search query`);
        // Convert platform names to TheGamesDB platform IDs
        const platformIds = platforms.map((platformName: string | number) => {
          if (typeof platformName === 'number') {
            return platformName.toString();
          }
          
          // Find the platform ID by matching the platform name to our mapping
          const foundId = Object.entries(this.platformMap).find(([, mappedPlatform]) => {
            const platformStr = platformName as string;
            return mappedPlatform === platformStr || 
                   mappedPlatform.toLowerCase().includes(platformStr.toLowerCase()) ||
                   platformStr.toLowerCase().includes(mappedPlatform.toLowerCase());
          });
          
          console.log(`🎯 TheGamesDB API: Platform "${platformName}" -> ID ${foundId ? foundId[0] : 'NOT FOUND'}`);
          return foundId ? foundId[0] : null;
        }).filter(id => id !== null);
        
        // Use TheGamesDB's platform filter syntax - use the 'platform' parameter with comma-separated IDs
        if (platformIds.length > 0) {
          params.platform = platformIds.join(',');
          console.log(`✅ TheGamesDB API: Platform filter applied - IDs: ${platformIds.join(', ')} (${platforms.join(', ')})`);
        } else {
          console.warn(`⚠️ TheGamesDB API: No valid platform IDs found for: ${platforms.join(', ')}`);
        }
      } else {
        console.log(`🔍 TheGamesDB API: No platform filters applied - searching all platforms`);
      }

      const data = await this.makeRequest<{
        count: number;
        games: TheGamesDBGame[];
      }>('/v1.1/Games/ByGameName', params);

      console.log(`🔍 TheGamesDB search for "${name}": ${data.games?.length || 0} results`);
      return data.games || [];
    } catch (error) {
      console.error('❌ TheGamesDB search error:', error);
      return [];
    }
  }

  // Search games by specific platform
  async searchByPlatform(platform: Platform, searchTerm: string = '', options: TheGamesDBSearchOptions = {}): Promise<TheGamesDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ TheGamesDB API not configured');
      return [];
    }

    // Find the platform ID for this platform
    const platformId = Object.entries(this.platformMap).find(([, mappedPlatform]) => 
      mappedPlatform === platform
    )?.[0];

    if (!platformId) {
      console.warn(`⚠️ Platform ID not found for: ${platform}`);
      return [];
    }

    console.log(`🎮 TheGamesDB searching platform "${platform}" (ID: ${platformId}) with term: "${searchTerm}"`);

    return this.searchByName(searchTerm || platform, {
      ...options,
      platforms: [parseInt(platformId)]
    });
  }

  async getGameDetails(gameId: number): Promise<TheGamesDBGame | null> {
    if (!this.isConfigured()) {
      return null;
    }

    try {
      const data = await this.makeRequest<{
        count: number;
        games: TheGamesDBGame[];
      }>('/v1/Games/ByGameID', {
        id: gameId,
        fields: 'players,publishers,genres,overview,rating,platform,coop,youtube,os,processor,ram,hdd,video,sound,alternates',
        include: 'boxart,platform'
      });

      return data.games?.[0] || null;
    } catch (error) {
      console.error(`❌ TheGamesDB game details error for ID ${gameId}:`, error);
      return null;
    }
  }

  async getGameImages(gameIds: number[], imageTypes?: string[]): Promise<Record<number, TheGamesDBImage[]>> {
    if (!this.isConfigured()) {
      return {};
    }

    try {
      const params: Record<string, string | number | string[]> = {
        games_id: gameIds.map(String)
      };

      if (imageTypes && imageTypes.length > 0) {
        params['filter[type]'] = imageTypes;
      }

      const data = await this.makeRequest<{
        count: number;
        base_url: TheGamesDBImageBaseUrl;
        images: Record<string, TheGamesDBImage[]>;
      }>('/v1/Games/Images', params);

      const result: Record<number, TheGamesDBImage[]> = {};
      Object.entries(data.images || {}).forEach(([gameId, images]) => {
        result[parseInt(gameId)] = images.map(img => ({
          ...img,
          filename: this.buildImageUrl(data.base_url, 'original', img.filename)
        }));
      });

      return result;
    } catch (error) {
      console.error('❌ TheGamesDB images error:', error);
      return {};
    }
  }

  private buildImageUrl(baseUrl: TheGamesDBImageBaseUrl, size: keyof TheGamesDBImageBaseUrl, filename: string): string {
    return `${baseUrl[size]}${filename}`;
  }

  convertToGame(tgdbGame: TheGamesDBGame, images?: TheGamesDBImage[]): Game {
    // Type-safe platform handling with fallback
    const platform = tgdbGame.platform ? this.platformMap[tgdbGame.platform] : undefined;
    if (!platform && tgdbGame.platform) {
      console.warn(`🔍 Unknown TheGamesDB platform ID: ${tgdbGame.platform} for game "${tgdbGame.game_title || 'Unknown'}"`);
    }
    const finalPlatform = platform || 'PC';
    console.log(`🎯 TheGamesDB "${tgdbGame.game_title || 'Unknown'}" platform ID ${tgdbGame.platform || 'unknown'} -> "${finalPlatform}"`);
    
    // Type-safe genre mapping with validation
    const genres = (tgdbGame.genres?.map(id => this.genreMap[id]).filter((genre): genre is string => Boolean(genre)) || []);
    
    // Type-safe developer and publisher mapping
    const developers = tgdbGame.developers?.map(id => this.developerMap[id]).filter((dev): dev is string => Boolean(dev)) || [];
    const publishers = tgdbGame.publishers?.map(id => this.publisherMap[id]).filter((pub): pub is string => Boolean(pub)) || [];

    // Type-safe image handling
    const boxartImages = images?.filter((img): img is TheGamesDBImage =>
      img?.type === 'boxart' && img?.side === 'front' && Boolean(img.filename)
    ) || [];
    const screenshotImages = images?.filter((img): img is TheGamesDBImage =>
      img?.type === 'screenshot' && Boolean(img.filename)
    ) || [];
    
    const coverImage = boxartImages[0]?.filename;
    const screenshots = screenshotImages.map(img => img.filename).filter(Boolean);

    // Type-safe YouTube link handling
    const youtubeLinks = tgdbGame.youtube && typeof tgdbGame.youtube === 'string'
      ? [`https://www.youtube.com/watch?v=${tgdbGame.youtube}`]
      : [];

    return {
      id: `tgdb_${tgdbGame.id}`,
      title: tgdbGame.game_title || 'Unknown Game',
      platforms: [finalPlatform],
      genres,
      developer: developers[0],
      publisher: publishers[0],
      release_date: tgdbGame.release_date,
      description: tgdbGame.overview,
      cover_image: coverImage,
      screenshots,
      youtube_links: youtubeLinks,
      metacritic_score: undefined, // TheGamesDB doesn't provide Metacritic scores
      igdb_id: undefined
    };
  }
}

class IGDBAPIService {
  // IGDB Platform ID mapping for filtering
  private readonly igdbPlatformIds: Record<Platform, number> = {
    'PC': 6,
    'PlayStation 5': 167,
    'PlayStation 4': 48,
    'PlayStation 3': 9,
    'PlayStation 2': 8,
    'PlayStation': 7,
    'Xbox Series X/S': 169,
    'Xbox One': 49,
    'Xbox 360': 12,
    'Xbox': 11,
    'Nintendo Switch': 130,
    'Nintendo 3DS': 37,
    'Nintendo DS': 20,
    'Wii U': 41,
    'Wii': 5,
    'GameCube': 21,
    'Nintendo 64': 4,
    'iOS': 39,
    'Android': 34,
    'Steam Deck': 170,
    'Mac': 14,
    'Linux': 3,
    '3DO': 50
  };

  // Enhanced platform mapping for IGDB names to our Platform types
  private readonly platformMap: Record<string, string> = {
    'PC (Microsoft Windows)': 'PC',
    'Mac': 'Mac',
    'PlayStation 5': 'PlayStation 5',
    'PlayStation 4': 'PlayStation 4',
    'PlayStation 3': 'PlayStation 3',
    'PlayStation 2': 'PlayStation 2',
    'PlayStation': 'PlayStation',
    'Xbox Series X|S': 'Xbox Series X/S',
    'Xbox One': 'Xbox One',
    'Xbox 360': 'Xbox 360',
    'Xbox': 'Xbox',
    'Nintendo Switch': 'Nintendo Switch',
    'Nintendo 3DS': 'Nintendo 3DS',
    'Nintendo DS': 'Nintendo DS',
    'Wii U': 'Wii U',
    'Wii': 'Wii',
    'Nintendo GameCube': 'GameCube',
    'Nintendo 64': 'Nintendo 64',
    'iOS': 'iOS',
    'Android': 'Android',
    'Steam Deck': 'Steam Deck',
    'Linux': 'Linux'
  };

  constructor() {
    console.log('🎮 IGDB API Service initialized - Enhanced gaming database access');
    this.testConnection();
  }

  // Test basic IGDB connection
  private async testConnection() {
    try {
      console.log('🔍 Testing IGDB connection via proxy...');
      
      // Test with a simple query
      const testQuery = 'fields name; where id = 1942; limit 1;';
      await this.makeRequest<IGDBGame[]>('games', testQuery);
      console.log('✅ IGDB proxy connection successful');
    } catch (error) {
      console.error('❌ IGDB connection test failed:', error);
    }
  }

  // Check if IGDB is properly configured (always true since handled server-side)
  isConfigured(): boolean {
    return true; // Server-side handles authentication
  }

  // Get platform ID for IGDB filtering
  getPlatformId(platform: Platform): number | undefined {
    return this.igdbPlatformIds[platform];
  }

  // Enhanced IGDB request method with retry logic
  private async makeRequest<T>(endpoint: string, query: string, retryCount: number = 0): Promise<T> {
    const maxRetries = 3;

    try {
      console.log(`🔍 IGDB ${endpoint} request:`, query.replace(/\s+/g, ' ').trim());

      const { data, error } = await supabase.functions.invoke('igdb-proxy', {
        body: { endpoint, query },
      });

      if (error) {
        console.error('❌ IGDB proxy error:', error);
        console.error('❌ Error details:', JSON.stringify(error, null, 2));
        
        // Retry on network errors or auth issues
        if (retryCount < maxRetries && (
          error.message.includes('network') ||
          error.message.includes('token') ||
          error.message.includes('401') ||
          error.message.includes('500')
        )) {
          console.log(`🔄 Retrying IGDB request (attempt ${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          return this.makeRequest<T>(endpoint, query, retryCount + 1);
        }
        
        throw new Error(`IGDB API error: ${error.message}`);
      }

      if (data?.error) {
        console.error('❌ IGDB response error:', data.error);
        throw new Error(`IGDB error: ${data.error}`);
      }

      if (!data) {
        throw new Error('No data received from IGDB API');
      }

      console.log(`✅ IGDB request successful - ${Array.isArray(data) ? data.length : 1} results`);
      return data as T;
    } catch (error) {
      if (retryCount < maxRetries && error instanceof Error && (
        error.message.includes('Failed to authenticate') ||
        error.message.includes('500')
      )) {
        console.log(`🔄 Retrying request (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.makeRequest<T>(endpoint, query, retryCount + 1);
      }
      throw error;
    }
  }

  // Enhanced search method with multiple strategies
  async search(searchTerm: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured, search unavailable');
      console.log('Debug: Client ID:', import.meta.env.VITE_IGDB_CLIENT_ID ? 'Present' : 'Missing');
      console.log('Debug: Client Secret:', import.meta.env.VITE_IGDB_CLIENT_SECRET ? 'Present' : 'Missing');
      return [];
    }

    const {
      limit = (options.platforms || []).length > 0 ? 75 : 50, // Higher limit for platform-specific searches
      platforms = [], // Extract platforms for filtering
      sortBy = 'relevance',
      sortDirection = 'desc',
      excludeDLC = false, // Changed to false to get more results
      excludeExpansions = false,
      searchType = 'smart'
    } = options;

    try {
      // Build minimal field list to avoid edge function issues
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating',
        'version_parent',
        'version_title'
      ];

      // Build search clause based on search type
      let searchClause = '';
      switch (searchType) {
        case 'exact':
          searchClause = `search "${searchTerm}";`;
          break;
        case 'fuzzy':
          searchClause = `where name ~ *"${searchTerm}"*;`;
          break;
        case 'smart':
        default:
          // Smart search: try exact first, then fuzzy if needed
          searchClause = `search "${searchTerm}";`;
          break;
      }

      // Build where conditions (simplified to avoid complex queries)
      const whereConditions: string[] = [];

      // Add platform filtering if specified
      if (platforms.length > 0) {
        console.log(`🎯 IGDB API: Applying platform filters to search query`);
        // Convert platform names to IGDB platform IDs for proper filtering
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          console.log(`🎯 IGDB API: Platform "${platformName}" -> ID ${platformId}`);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
          console.log(`✅ IGDB API: Platform filter applied - IDs: ${platformIds.join(', ')} (${platforms.join(', ')})`);
        } else {
          // Fallback to name-based filtering if no platform IDs found
          const platformNames = platforms.map(p => `"${p}"`).join(',');
          whereConditions.push(`platforms.name = (${platformNames})`);
          console.log(`⚠️ IGDB API: Using name-based platform filtering: ${platforms.join(', ')}`);
        }
      } else {
        console.log(`🔍 IGDB API: No platform filters applied - searching all platforms`);
      }

      // Only add basic filtering to avoid query complexity issues
      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      // Combine clauses
      let whereClause = '';
      if (searchType === 'fuzzy') {
        // For fuzzy search, combine search with where conditions
        const fuzzyConditions = [`name ~ *"${searchTerm}"*`];
        
        // Add platform filtering for fuzzy search
        if (platforms.length > 0) {
          // Convert platform names to IGDB platform IDs for proper filtering
          const platformIds = platforms.map(platformName => {
            const platformId = this.getPlatformId(platformName as Platform);
            return platformId;
          }).filter(id => id !== undefined);

          if (platformIds.length > 0) {
            fuzzyConditions.push(`platforms = (${platformIds.join(',')})`);
          } else {
            // Fallback to name-based filtering if no platform IDs found
            const platformNames = platforms.map(p => `"${p}"`).join(',');
            fuzzyConditions.push(`platforms.name = (${platformNames})`);
          }
        }
        
        // Add other conditions
        if (excludeDLC) {
          fuzzyConditions.push('category != 1');
        }
        if (excludeExpansions) {
          fuzzyConditions.push('category != 2');
        }
        
        if (fuzzyConditions.length > 0) {
          whereClause = `where ${fuzzyConditions.join(' & ')};`;
        }
        searchClause = '';
      } else if (whereConditions.length > 0) {
        whereClause = `where ${whereConditions.join(' & ')};`;
      }

      // Build sort clause
      let sortClause = '';
      switch (sortBy) {
        case 'rating':
          sortClause = `sort aggregated_rating ${sortDirection};`;
          break;
        case 'release_date':
          sortClause = `sort first_release_date ${sortDirection};`;
          break;
        case 'name':
          sortClause = `sort name ${sortDirection};`;
          break;
        case 'popularity':
          sortClause = 'sort follows desc;';
          break;
        case 'relevance':
        default:
          // Let IGDB handle relevance sorting for search queries
          sortClause = '';
          break;
      }

      // Build complete query
      const queryParts = [
        searchClause,
        `fields ${fields.join(', ')};`,
        whereClause,
        sortClause,
        `limit ${limit};`
      ].filter(Boolean);

      const query = queryParts.join('\n');
      console.log('🔍 Final IGDB query:', query);
      
      const games = await this.makeRequest<IGDBGame[]>('games', query);

      if (!Array.isArray(games)) {
        console.error('❌ IGDB response is not an array:', games);
        return [];
      }

      // Expand search results to include game versions (different platform releases)
      const expandedGames = await this.expandGameVersions(games);
      console.log(`🔍 Expanded ${games.length} games to ${expandedGames.length} including versions`);

      // If smart search and few results, try different approaches to get more results
      if (searchType === 'smart' && expandedGames.length < 5 && searchTerm.length > 2) {
        console.log('🔍 Smart search: trying fuzzy search for more results...');
        try {
          const fuzzyResults = await this.search(searchTerm, { 
            ...options, 
            searchType: 'fuzzy',
            limit: Math.max(limit - expandedGames.length, 10) // Ensure we get at least 10 more
          });
          
          // Combine results, removing duplicates
          const existingIds = new Set(expandedGames.map(g => g.id));
          const newResults = fuzzyResults.filter(g => !existingIds.has(g.id));
          expandedGames.push(...newResults);
          
          console.log(`🔍 Combined search results: ${expandedGames.length} total games`);
        } catch (fuzzyError) {
          console.warn('⚠️ Fuzzy search fallback failed:', fuzzyError);
        }
      }

      return expandedGames;
    } catch (error) {
      console.error('❌ IGDB search error:', error);
      
      // Try a simplified fallback search
      try {
        console.log('🔄 Attempting simplified fallback search...');
        const fallbackQuery = `search "${searchTerm}"; fields name, platforms.name, genres.name; limit ${Math.min(limit, 50)};`;
        console.log('🔍 Fallback query:', fallbackQuery);
        
        const fallbackGames = await this.makeRequest<IGDBGame[]>('games', fallbackQuery);
        console.log(`✅ Fallback search succeeded: ${fallbackGames.length} games found`);
        return fallbackGames;
      } catch (fallbackError) {
        console.error('❌ Fallback search also failed:', fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  // Search by genre
  async searchByGenre(genre: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    console.log(`🎯 Searching IGDB by genre: ${genre}`);
    return this.search('', { ...options, genres: [genre] });
  }

  // Search by platform with proper IGDB platform filtering
  async searchByPlatform(platform: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    console.log(`🎮 Searching IGDB by platform: ${platform}`);
    
    // Get the platform ID for IGDB filtering
    const platformKey = platform as Platform;
    const platformId = this.igdbPlatformIds[platformKey];
    
    if (!platformId) {
      console.warn(`⚠️ Unknown platform for IGDB filtering: ${platform}`);
      return this.search('', options);
    }

    return this.searchWithPlatformFilter('', platformId, options);
  }

  // Enhanced search with platform filtering
  async searchWithPlatformFilter(searchTerm: string, platformId: number, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const {
      limit = 50,
      sortBy = 'relevance',
      sortDirection = 'desc',
      excludeDLC = true,
      excludeExpansions = true
    } = options;

    try {
      const fields = [
        'name',
        'platforms.name',
        'genres.name',
        'first_release_date',
        'summary',
        'cover.image_id',
        'aggregated_rating',
        'version_parent',
        'version_title'
      ];

      // Build where conditions with platform filtering
      const whereConditions: string[] = [`platforms = (${platformId})`];
      
      if (excludeDLC) {
        whereConditions.push('category != 1');
      }
      if (excludeExpansions) {
        whereConditions.push('category != 2');
      }

      // Build complete query with platform filter
      const queryParts = [];
      
      if (searchTerm.trim()) {
        queryParts.push(`search "${searchTerm}";`);
      }
      
      queryParts.push(`fields ${fields.join(', ')};`);
      queryParts.push(`where ${whereConditions.join(' & ')};`);
      
      // Add sorting
      if (sortBy !== 'relevance') {
        let sortField = 'aggregated_rating';
        switch (sortBy) {
          case 'release_date':
            sortField = 'first_release_date';
            break;
          case 'name':
            sortField = 'name';
            break;
          case 'popularity':
            sortField = 'follows';
            break;
        }
        queryParts.push(`sort ${sortField} ${sortDirection};`);
      }
      
      queryParts.push(`limit ${limit};`);

      const query = queryParts.join('\n');
      console.log(`🔍 IGDB platform-filtered query (Platform ID: ${platformId}):`, query);
      
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      console.log(`✅ IGDB platform search completed: ${games.length} games found for platform ID ${platformId}`);
      
      return games;
    } catch (error) {
      console.error('❌ IGDB platform search error:', error);
      return [];
    }
  }

  // Search by developer
  async searchByDeveloper(developer: string, options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const { limit = 20 } = options;

    try {
      const query = `
        fields name, platforms.name, genres.name, involved_companies.company.name,
               involved_companies.developer, involved_companies.publisher,
               first_release_date, summary, cover.url, cover.image_id,
               aggregated_rating, category;
        where involved_companies.company.name ~ *"${developer}"* & involved_companies.developer = true;
        sort aggregated_rating desc;
        limit ${limit};
      `;

      console.log(`👨‍💻 Searching IGDB by developer: ${developer}`);
      return await this.makeRequest<IGDBGame[]>('games', query);
    } catch (error) {
      console.error('❌ IGDB developer search error:', error);
      throw error;
    }
  }

  // Get popular games
  async getPopularGames(options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const { limit = 30, platforms = [] } = options;

    try {
      const fields = [
        'name', 'platforms.name', 'genres.name', 'involved_companies.company.name',
        'involved_companies.developer', 'involved_companies.publisher',
        'first_release_date', 'summary', 'cover.url', 'cover.image_id',
        'aggregated_rating', 'category'
      ];

      const whereConditions = [
        'aggregated_rating > 75',
        'first_release_date > 1577836800' // Since 2020
      ];

      if (platforms.length > 0) {
        // Convert platform names to IGDB platform IDs for proper filtering
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
        } else {
          // Fallback to name-based filtering if no platform IDs found
          const platformNames = platforms.map(p => `"${p}"`).join(',');
          whereConditions.push(`platforms.name = (${platformNames})`);
        }
      }

      const query = `
        fields ${fields.join(', ')};
        where ${whereConditions.join(' & ')};
        sort aggregated_rating desc;
        limit ${limit};
      `;

      console.log('🔥 Getting popular games from IGDB');
      return await this.makeRequest<IGDBGame[]>('games', query);
    } catch (error) {
      console.error('❌ IGDB popular games error:', error);
      throw error;
    }
  }

  // Get new releases
  async getNewReleases(options: IGDBSearchOptions = {}): Promise<IGDBGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return [];
    }

    const { limit = 20, platforms = [] } = options;
    const thirtyDaysAgo = Math.floor((Date.now() - 30 * 24 * 60 * 60 * 1000) / 1000);

    try {
      const whereConditions = [
        `first_release_date > ${thirtyDaysAgo}`,
        'category = 0' // Main games only
      ];

      if (platforms.length > 0) {
        // Convert platform names to IGDB platform IDs for proper filtering
        const platformIds = platforms.map(platformName => {
          const platformId = this.getPlatformId(platformName as Platform);
          return platformId;
        }).filter(id => id !== undefined);

        if (platformIds.length > 0) {
          whereConditions.push(`platforms = (${platformIds.join(',')})`);
        } else {
          // Fallback to name-based filtering if no platform IDs found
          const platformNames = platforms.map(p => `"${p}"`).join(',');
          whereConditions.push(`platforms.name = (${platformNames})`);
        }
      }

      const query = `
        fields name, platforms.name, genres.name, involved_companies.company.name,
               involved_companies.developer, involved_companies.publisher,
               first_release_date, summary, cover.url, cover.image_id,
               aggregated_rating;
        where ${whereConditions.join(' & ')};
        sort first_release_date desc;
        limit ${limit};
      `;

      console.log('🆕 Getting new releases from IGDB');
      return await this.makeRequest<IGDBGame[]>('games', query);
    } catch (error) {
      console.error('❌ IGDB new releases error:', error);
      throw error;
    }
  }

  // Get game details by ID
  async getGameDetails(gameId: number): Promise<IGDBGame | null> {
    if (!this.isConfigured()) {
      console.warn('⚠️ IGDB API not configured');
      return null;
    }

    try {
      const query = `
        fields name, platforms.name, genres.name, involved_companies.company.name,
               involved_companies.developer, involved_companies.publisher,
               first_release_date, summary, cover.url, cover.image_id,
               screenshots.url, screenshots.image_id, aggregated_rating,
               videos.video_id, videos.name, similar_games.name, themes.name;
        where id = ${gameId};
      `;

      console.log(`🔍 Getting IGDB game details for ID: ${gameId}`);
      const games = await this.makeRequest<IGDBGame[]>('games', query);
      return games[0] || null;
    } catch (error) {
      console.error('❌ IGDB game details error:', error);
      return null;
    }
  }

  // Expand game results to include different platform versions
  private async expandGameVersions(games: IGDBGame[]): Promise<IGDBGame[]> {
    const expandedGames: IGDBGame[] = [...games];
    
    try {
      // Find parent games (games that might have versions)
      const parentGames = games.filter(g => g.version_parent === null || g.version_parent === undefined);
      
      for (const parent of parentGames) {
        try {
          // Search for versions of this game
          const versionsQuery = `
            fields name, platforms.name, genres.name, first_release_date, summary, cover.image_id, aggregated_rating, version_parent, version_title;
            where version_parent = ${parent.id};
            limit 20;
          `;
          
          const versions = await this.makeRequest<IGDBGame[]>('games', versionsQuery);
          
          if (versions && versions.length > 0) {
            console.log(`🔍 Found ${versions.length} versions for "${parent.name}"`);
            expandedGames.push(...versions);
          }
        } catch (versionError) {
          console.warn(`⚠️ Could not fetch versions for ${parent.name}:`, versionError);
        }
      }
    } catch (error) {
      console.warn('⚠️ Game version expansion failed:', error);
      return games; // Return original games if expansion fails
    }
    
    return expandedGames;
  }

  // Search YouTube videos for a game
  async searchYouTubeVideos(query: string, maxResults: number = 5): Promise<string[]> {
    const apiKey = import.meta.env.VITE_YOUTUBE_API_KEY;
    
    if (!apiKey || apiKey === 'your_youtube_api_key') {
      console.warn('⚠️ YouTube API key not configured');
      return [];
    }

    try {
      const searchQuery = encodeURIComponent(`${query} gameplay trailer review`);
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${searchQuery}&maxResults=${maxResults}&key=${apiKey}`
      );

      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data: YouTubeSearchResponse = await response.json();
      const videoUrls = data.items.map(item => `https://www.youtube.com/watch?v=${item.id.videoId}`);
      
      console.log(`📺 Found ${videoUrls.length} YouTube videos for: ${query}`);
      return videoUrls;
    } catch (error) {
      console.error('❌ YouTube search error:', error);
      return [];
    }
  }

  // Convert IGDB game to standard format
  convertToGame(igdbGame: IGDBGame): Game {
    // Type-safe platform handling with null checks
    const rawPlatforms = igdbGame.platforms?.map(p => p?.name).filter((name): name is string => Boolean(name)) || [];
    
    console.log(`🔍 IGDB Game "${igdbGame.name || 'Unknown'}" platforms:`, rawPlatforms);
    
    // Normalize platform names to our standard format
    const normalizedPlatforms = rawPlatforms.map(platformName => {
      const normalized = this.platformMap[platformName] || platformName;
      console.log(`🎯 Normalizing platform "${platformName}" -> "${normalized}"`);
      return normalized;
    });
    
    console.log(`🎯 Final normalized platforms for "${igdbGame.name || 'Unknown'}":`, normalizedPlatforms);

    // Type-safe company handling
    const companies = igdbGame.involved_companies || [];
    const developer = companies.find(c => c?.developer && c?.company?.name)?.company?.name;
    const publisher = companies.find(c => c?.publisher && c?.company?.name)?.company?.name;

    // Type-safe release date formatting
    const releaseDate = igdbGame.first_release_date && typeof igdbGame.first_release_date === 'number'
      ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0]
      : undefined;

    // Type-safe cover image URL building
    const coverImage = igdbGame.cover?.image_id && typeof igdbGame.cover.image_id === 'string'
      ? `https://images.igdb.com/igdb/image/upload/t_cover_big_2x/${igdbGame.cover.image_id}.jpg`
      : undefined;

    // Type-safe screenshot URLs building
    const screenshots = igdbGame.screenshots?.map(s => {
      if (s?.image_id && typeof s.image_id === 'string') {
        return `https://images.igdb.com/igdb/image/upload/t_screenshot_huge/${s.image_id}.jpg`;
      }
      if (s?.url && typeof s.url === 'string') {
        return `https:${s.url.replace('t_thumb', 't_screenshot_huge')}`;
      }
      return '';
    }).filter(Boolean) || [];

    // Type-safe YouTube links building
    const youtubeLinks = igdbGame.videos?.map(v =>
      v?.video_id && typeof v.video_id === 'string'
        ? `https://www.youtube.com/watch?v=${v.video_id}`
        : ''
    ).filter(Boolean) || [];

    // Type-safe version title handling
    const gameTitle = igdbGame.version_title && typeof igdbGame.version_title === 'string'
      ? `${igdbGame.name || 'Unknown'} (${igdbGame.version_title})`
      : igdbGame.name || 'Unknown Game';

    return {
      id: `igdb_${igdbGame.id}`,
      title: gameTitle,
      platforms: normalizedPlatforms,
      genres: (igdbGame.genres?.map(g => g?.name).filter((name): name is string => Boolean(name)) || []),
      developer,
      publisher,
      release_date: releaseDate,
      description: igdbGame.summary,
      cover_image: coverImage,
      screenshots,
      metacritic_score: igdbGame.aggregated_rating && typeof igdbGame.aggregated_rating === 'number'
        ? Math.round(igdbGame.aggregated_rating)
        : undefined,
      youtube_links: youtubeLinks,
      igdb_id: igdbGame.id.toString(),
    };
  }
}

// Export API services
export const igdbAPI = new IGDBAPIService();
export const theGamesDBAPI = new TheGamesDBAPIService();
export const gameAPI = igdbAPI; // Keep backward compatibility
export { IGDBAPIService, TheGamesDBAPIService };
export type { IGDBGame, IGDBSearchOptions, TheGamesDBGame, TheGamesDBSearchOptions };