/**
 * API Key Store Service - Secure management of user API keys
 * Handles encryption, storage, and retrieval of platform API keys
 */

import { supabase } from './supabase';
import { encryptValue, decryptValue, isEncryptionAvailable } from './encryption';

// Supported platforms and services for API keys
export type Platform = 'steam';
export type ArtworkService = 'igdb' | 'serpapi' | 'steamgriddb' | 'thegamesdb' | 'openai' | 'deepseek' | 'gemini';

// API key types for different platforms and services
export type KeyType = 'api_key' | 'client_id' | 'client_secret';

export interface ApiKeyData {
  platform?: Platform;
  service?: ArtworkService;
  keyType: KeyType;
  value: string;
  label?: string; // User-friendly label
}

export interface StoredApiKey {
  id: string;
  platform?: Platform;
  service?: ArtworkService;
  keyName: KeyType;
  createdAt: string;
  updatedAt: string;
  label?: string;
}

export interface PlatformKeys {
  [key: string]: string; // KeyType -> decrypted value
}

export interface AllPlatformKeys {
  [platform: string]: PlatformKeys;
}

class ApiKeyStoreService {
  private userId: string | null = null;

  /**
   * Initialize the service with current user
   */
  async initialize(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    this.userId = user?.id || null;
    
    if (!this.userId) {
      throw new Error('User must be authenticated to manage API keys');
    }

    if (!isEncryptionAvailable()) {
      throw new Error('Web Crypto API not available. Secure key storage requires HTTPS.');
    }
  }

  /**
   * Store an encrypted API key for a platform or service
   */
  async storeKey(keyData: ApiKeyData): Promise<void> {
    await this.initialize();

    if (!keyData.value.trim()) {
      throw new Error('API key value cannot be empty');
    }

    if (!keyData.platform && !keyData.service) {
      throw new Error('Either platform or service must be specified');
    }

    const encrypted = await encryptValue(keyData.value, this.userId!);

    const { error } = await supabase
      .from('user_api_keys')
      .upsert({
        user_id: this.userId,
        platform: keyData.platform || null,
        service: keyData.service || null,
        key_name: keyData.keyType,
        encrypted_value: JSON.stringify(encrypted),
        label: keyData.label || null
      });

    if (error) {
      throw new Error(`Failed to store API key: ${error.message}`);
    }
  }

  /**
   * Retrieve and decrypt an API key
   */
  async getKey(platform: Platform, keyType: KeyType): Promise<string | null> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('encrypted_value')
      .eq('user_id', this.userId)
      .eq('platform', platform)
      .eq('key_name', keyType)
      .single();

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      return null;
    }

    if (!data) {
      return null;
    }

    try {
      const encryptedData = JSON.parse(data.encrypted_value);
      return await decryptValue(encryptedData, this.userId!);
    } catch (error) {
      console.error('Failed to decrypt API key:', error);
      return null;
    }
  }

  /**
   * Get all keys for a specific platform
   */
  async getPlatformKeys(platform: Platform): Promise<PlatformKeys> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('key_name, encrypted_value')
      .eq('user_id', this.userId)
      .eq('platform', platform);

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      return {};
    }

    const keys: PlatformKeys = {};

    for (const row of data || []) {
      try {
        const encryptedData = JSON.parse(row.encrypted_value);
        const decryptedValue = await decryptValue(encryptedData, this.userId!);
        keys[row.key_name] = decryptedValue;
      } catch (error) {
        console.error(`Failed to decrypt key ${row.key_name} for platform ${platform}:`, error);
      }
    }

    return keys;
  }

  /**
   * Get all stored API keys (metadata only, no values)
   */
  async getStoredKeys(): Promise<StoredApiKey[]> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('id, platform, key_name, created_at, updated_at')
      .eq('user_id', this.userId)
      .order('platform', { ascending: true })
      .order('key_name', { ascending: true });

    if (error) {
      throw new Error(`Failed to retrieve stored keys: ${error.message}`);
    }

    return (data || []).map(row => ({
      id: row.id,
      platform: row.platform as Platform,
      keyName: row.key_name as KeyType,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  }

  /**
   * Delete an API key
   */
  async deleteKey(platform: Platform, keyType: KeyType): Promise<void> {
    await this.initialize();

    const { error } = await supabase
      .from('user_api_keys')
      .delete()
      .eq('user_id', this.userId)
      .eq('platform', platform)
      .eq('key_name', keyType);

    if (error) {
      throw new Error(`Failed to delete API key: ${error.message}`);
    }
  }

  /**
   * Delete all keys for a platform
   */
  async deletePlatformKeys(platform: Platform): Promise<void> {
    await this.initialize();

    const { error } = await supabase
      .from('user_api_keys')
      .delete()
      .eq('user_id', this.userId)
      .eq('platform', platform);

    if (error) {
      throw new Error(`Failed to delete platform keys: ${error.message}`);
    }
  }

  /**
   * Check if user has keys for a platform
   */
  async hasPlatformKeys(platform: Platform): Promise<boolean> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('id')
      .eq('user_id', this.userId)
      .eq('platform', platform)
      .limit(1);

    if (error) {
      return false;
    }

    return (data?.length || 0) > 0;
  }

  /**
   * Get available platforms with their key requirements
   */
  static getPlatformRequirements(): Record<Platform, { label: string; keys: { type: KeyType; label: string; required: boolean }[] }> {
    return {
      steam: {
        label: 'Steam',
        keys: [
          { type: 'api_key', label: 'Steam API Key', required: true },
        ],
      },
    };
  }

  /**
   * Test if stored keys are valid by attempting decryption
   */
  async testStoredKeys(): Promise<{ platform: Platform; keyType: KeyType; valid: boolean }[]> {
    await this.initialize();

    const storedKeys = await this.getStoredKeys();
    const results = [];

    for (const key of storedKeys) {
      try {
        const value = await this.getKey(key.platform, key.keyName);
        results.push({
          platform: key.platform,
          keyType: key.keyName,
          valid: !!value,
        });
      } catch {
        results.push({
          platform: key.platform,
          keyType: key.keyName,
          valid: false,
        });
      }
    }

    return results;
  }

  /**
   * Get API key for a specific artwork service
   */
  async getServiceKey(service: ArtworkService, keyType: KeyType): Promise<string | null> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('encrypted_value')
      .eq('user_id', this.userId)
      .eq('service', service)
      .eq('key_name', keyType)
      .single();

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      return null;
    }

    if (!data) {
      return null;
    }

    try {
      const encryptedData = JSON.parse(data.encrypted_value);
      return await decryptValue(encryptedData, this.userId!);
    } catch (error) {
      console.error('Failed to decrypt API key:', error);
      return null;
    }
  }

  /**
   * Get all keys for a specific artwork service
   */
  async getServiceKeys(service: ArtworkService): Promise<Array<{ keyType: KeyType; value: string; label?: string }>> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('key_name, encrypted_value, label')
      .eq('user_id', this.userId)
      .eq('service', service);

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      return [];
    }

    const keys = [];

    for (const row of data || []) {
      try {
        const encryptedData = JSON.parse(row.encrypted_value);
        const decryptedValue = await decryptValue(encryptedData, this.userId!);
        keys.push({
          keyType: row.key_name as KeyType,
          value: decryptedValue,
          label: row.label
        });
      } catch (error) {
        console.error(`Failed to decrypt key ${row.key_name} for service ${service}:`, error);
      }
    }

    return keys;
  }

  /**
   * Get all available API keys for a service (multiple keys of same type)
   */
  async getAllServiceKeys(service: ArtworkService, keyType: KeyType): Promise<Array<{ id: string; value: string; label?: string }>> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('id, encrypted_value, label')
      .eq('user_id', this.userId)
      .eq('service', service)
      .eq('key_name', keyType);

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      return [];
    }

    const keys = [];

    for (const row of data || []) {
      try {
        const encryptedData = JSON.parse(row.encrypted_value);
        const decryptedValue = await decryptValue(encryptedData, this.userId!);
        keys.push({
          id: row.id,
          value: decryptedValue,
          label: row.label
        });
      } catch (error) {
        console.error(`Failed to decrypt key ${row.id} for service ${service}:`, error);
      }
    }

    return keys;
  }

  /**
   * Delete API key for a service
   */
  async deleteServiceKey(service: ArtworkService, keyType: KeyType, keyId?: string): Promise<void> {
    await this.initialize();

    let query = supabase
      .from('user_api_keys')
      .delete()
      .eq('user_id', this.userId)
      .eq('service', service)
      .eq('key_name', keyType);

    if (keyId) {
      query = query.eq('id', keyId);
    }

    const { error } = await query;

    if (error) {
      throw new Error(`Failed to delete API key: ${error.message}`);
    }
  }

  /**
   * Check if user has keys for a service
   */
  async hasServiceKeys(service: ArtworkService): Promise<boolean> {
    await this.initialize();

    const { data, error } = await supabase
      .from('user_api_keys')
      .select('id')
      .eq('user_id', this.userId)
      .eq('service', service)
      .limit(1);

    if (error) {
      return false;
    }

    return (data?.length || 0) > 0;
  }

  /**
   * Fallback to environment variables if user keys not available
   */
  getEnvironmentKey(platform?: Platform, service?: ArtworkService, keyType?: KeyType): string | null {
    const envMap: Record<string, string> = {
      // Platform keys
      'steam_api_key': import.meta.env.VITE_STEAM_API_KEY || '',
      
      // Service keys
      'igdb_client_id': import.meta.env.VITE_IGDB_CLIENT_ID || '',
      'igdb_client_secret': import.meta.env.VITE_IGDB_CLIENT_SECRET || '',
      'serpapi_api_key': import.meta.env.VITE_SERPAPI_KEY || '',
      'steamgriddb_api_key': import.meta.env.VITE_STEAMGRIDDB_API_KEY || '',
      'thegamesdb_api_key': import.meta.env.VITE_THEGAMESDB_API_KEY || '',
      'openai_api_key': import.meta.env.VITE_OPENAI_API_KEY || '',
      'deepseek_api_key': import.meta.env.VITE_DEEPSEEK_API_KEY || '',
      'gemini_api_key': import.meta.env.VITE_GEMINI_API_KEY || '',
    };

    if (platform && keyType) {
      const envKey = `${platform}_${keyType}`;
      return envMap[envKey] || null;
    }

    if (service && keyType) {
      const envKey = `${service}_${keyType}`;
      return envMap[envKey] || null;
    }

    return null;
  }

  /**
   * Get key with fallback to environment variables (for platforms)
   */
  async getKeyWithFallback(platform: Platform, keyType: KeyType): Promise<string | null> {
    try {
      // Try to get user-specific key first
      const userKey = await this.getKey(platform, keyType);
      if (userKey) {
        return userKey;
      }
    } catch (error) {
      console.warn('Failed to get user key, falling back to environment:', error);
    }

    // Fallback to environment variables
    return this.getEnvironmentKey(platform, undefined, keyType);
  }

  /**
   * Get service key with fallback to environment variables
   */
  async getServiceKeyWithFallback(service: ArtworkService, keyType: KeyType): Promise<string | null> {
    try {
      // Try to get user-specific key first
      const userKey = await this.getServiceKey(service, keyType);
      if (userKey) {
        return userKey;
      }
    } catch (error) {
      console.warn('Failed to get user service key, falling back to environment:', error);
    }

    // Fallback to environment variables
    return this.getEnvironmentKey(undefined, service, keyType);
  }

  /**
   * Get all available keys for a service with fallback to environment
   */
  async getAllAvailableServiceKeys(service: ArtworkService, keyType: KeyType): Promise<Array<{ id: string; value: string; label?: string; source: 'user' | 'env' }>> {
    const keys = [];

    // Get user-stored keys
    try {
      const userKeys = await this.getAllServiceKeys(service, keyType);
      keys.push(...userKeys.map(key => ({ ...key, source: 'user' as const })));
    } catch (error) {
      console.warn('Failed to get user service keys:', error);
    }

    // Add environment key if available and not already present
    const envKey = this.getEnvironmentKey(undefined, service, keyType);
    if (envKey && !keys.some(k => k.value === envKey)) {
      keys.push({
        id: `env_${service}_${keyType}`,
        value: envKey,
        label: `Environment ${service.toUpperCase()} Key`,
        source: 'env' as const
      });
    }

    return keys;
  }
}

// Export singleton instance
export const apiKeyStore = new ApiKeyStoreService();