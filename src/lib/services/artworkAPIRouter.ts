/**
 * Artwork API Router Service
 * Intelligently routes artwork requests across different API services
 * Handles API key selection, rate limiting, and fallback strategies
 */

import { rateLimitManager, ArtworkService } from './rateLimitManager';
import { apiKeyStore, KeyType } from '../apiKeyStore';

export type ArtworkRequestType = 'search' | 'cover' | 'screenshot' | 'artwork' | 'bulk';
export type RequestPriority = 'high' | 'medium' | 'low';

export interface ArtworkRequest {
  id: string;
  type: ArtworkRequestType;
  priority: RequestPriority;
  service?: ArtworkService; // Preferred service
  data: unknown; // Request-specific data
  callback: (result: ArtworkResult) => void;
  onProgress?: (progress: number) => void;
}

export interface ArtworkResult {
  success: boolean;
  data?: unknown;
  error?: string;
  service: ArtworkService;
  keyId: string;
  responseTimeMs: number;
}

export interface ServiceConfig {
  service: ArtworkService;
  keyType: KeyType;
  priority: number; // Higher number = higher priority
  costPerRequest?: number; // Cost in arbitrary units
  qualityRating?: number; // 1-5 quality rating
  supportedRequestTypes: ArtworkRequestType[];
}

export interface RoutingOptions {
  preferredServices?: ArtworkService[];
  excludeServices?: ArtworkService[];
  maxCostPerRequest?: number;
  requireHighQuality?: boolean;
  allowEnvironmentKeys?: boolean;
}

class ArtworkAPIRouter {
  private requestQueue: ArtworkRequest[] = [];
  private activeRequests = new Map<string, ArtworkRequest>();
  private processing = false;

  // Service configurations
  private readonly serviceConfigs: Record<ArtworkService, ServiceConfig> = {
    igdb: {
      service: 'igdb',
      keyType: 'client_id',
      priority: 90, // High priority for official data
      costPerRequest: 0, // Free
      qualityRating: 5, // Highest quality
      supportedRequestTypes: ['search', 'cover', 'artwork', 'screenshot']
    },
    steamgriddb: {
      service: 'steamgriddb',
      keyType: 'api_key',
      priority: 85, // High priority for artwork
      costPerRequest: 0, // Free
      qualityRating: 5, // High quality community artwork
      supportedRequestTypes: ['cover', 'artwork']
    },
    thegamesdb: {
      service: 'thegamesdb',
      keyType: 'api_key',
      priority: 80, // High priority for official game data
      costPerRequest: 0, // Free
      qualityRating: 4, // Good quality official data
      supportedRequestTypes: ['search', 'cover', 'artwork']
    },
    serpapi: {
      service: 'serpapi',
      keyType: 'api_key',
      priority: 70, // Medium priority
      costPerRequest: 0.01, // Small cost per request
      qualityRating: 3, // Variable quality
      supportedRequestTypes: ['search', 'cover', 'bulk']
    },
    openai: {
      service: 'openai',
      keyType: 'api_key',
      priority: 60, // Lower priority for artwork
      costPerRequest: 0.02, // Higher cost
      qualityRating: 4, // Good quality analysis
      supportedRequestTypes: ['search']
    },
    deepseek: {
      service: 'deepseek',
      keyType: 'api_key',
      priority: 55, // Lower priority
      costPerRequest: 0.005, // Lower cost alternative
      qualityRating: 3, // Good quality
      supportedRequestTypes: ['search']
    },
    gemini: {
      service: 'gemini',
      keyType: 'api_key',
      priority: 50, // Lower priority
      costPerRequest: 0.01, // Medium cost
      qualityRating: 4, // Good quality
      supportedRequestTypes: ['search']
    }
  };

  /**
   * Route a single artwork request
   */
  async routeRequest(
    type: ArtworkRequestType,
    data: unknown,
    options: RoutingOptions = {}
  ): Promise<ArtworkResult> {
    const requestId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      const request: ArtworkRequest = {
        id: requestId,
        type,
        priority: this.determinePriority(type),
        service: options.preferredServices?.[0],
        data,
        callback: resolve
      };

      // Try to process immediately for high priority requests
      if (request.priority === 'high') {
        this.processRequest(request, options).catch(reject);
      } else {
        // Add to queue for lower priority requests
        this.addToQueue(request);
        this.processQueue();
      }
    });
  }

  /**
   * Queue multiple requests for batch processing
   */
  async routeMultipleRequests(
    requests: Array<{ type: ArtworkRequestType; data: unknown }>,
    options: RoutingOptions = {}
  ): Promise<ArtworkResult[]> {
    const promises = requests.map(req => 
      this.routeRequest(req.type, req.data, options)
    );

    return Promise.all(promises);
  }

  /**
   * Get the best service for a request type
   */
  async getBestService(
    requestType: ArtworkRequestType,
    options: RoutingOptions = {}
  ): Promise<{ service: ArtworkService; keyId: string } | null> {
    // Filter services that support this request type
    let availableServices = Object.values(this.serviceConfigs)
      .filter(config => config.supportedRequestTypes.includes(requestType));

    // Apply service preferences and exclusions
    if (options.preferredServices?.length) {
      const preferred = availableServices.filter(config => 
        options.preferredServices!.includes(config.service)
      );
      if (preferred.length > 0) {
        availableServices = preferred;
      }
    }

    if (options.excludeServices?.length) {
      availableServices = availableServices.filter(config => 
        !options.excludeServices!.includes(config.service)
      );
    }

    // Apply cost filtering
    if (options.maxCostPerRequest !== undefined) {
      availableServices = availableServices.filter(config => 
        (config.costPerRequest || 0) <= options.maxCostPerRequest!
      );
    }

    // Apply quality filtering
    if (options.requireHighQuality) {
      availableServices = availableServices.filter(config => 
        (config.qualityRating || 0) >= 4
      );
    }

    // Find services with available API keys
    const servicesWithKeys = [];
    for (const config of availableServices) {
      try {
        const availableKeys = await apiKeyStore.getAllAvailableServiceKeys(
          config.service, 
          config.keyType
        );

        // Filter environment keys if not allowed
        const validKeys = options.allowEnvironmentKeys !== false ? 
          availableKeys : 
          availableKeys.filter(key => key.source === 'user');

        if (validKeys.length > 0) {
          // Get best available key using rate limit manager
          const keyIds = validKeys.map(k => k.id);
          const bestKeyId = rateLimitManager.getBestKey(config.service, keyIds);
          
          if (bestKeyId && rateLimitManager.canMakeRequest(config.service, bestKeyId)) {
            servicesWithKeys.push({
              ...config,
              keyId: bestKeyId
            });
          }
        }
      } catch (error) {
        console.warn(`Failed to check keys for service ${config.service}:`, error);
      }
    }

    if (servicesWithKeys.length === 0) {
      console.warn(`⚠️ No services with valid API keys found for ${requestType}. Available services: ${availableServices.map(s => s.service).join(', ')}`);
      return null;
    }

    // Sort by priority and return the best option
    servicesWithKeys.sort((a, b) => b.priority - a.priority);
    const best = servicesWithKeys[0];
    
    return {
      service: best.service,
      keyId: best.keyId
    };
  }

  /**
   * Process a single request
   */
  private async processRequest(
    request: ArtworkRequest, 
    options: RoutingOptions = {}
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Get the best service for this request
      const serviceSelection = await this.getBestService(request.type, options);
      
      if (!serviceSelection) {
        request.callback({
          success: false,
          error: 'No available services with valid API keys',
          service: 'igdb', // Default fallback
          keyId: 'none',
          responseTimeMs: Date.now() - startTime
        });
        return;
      }

      const { service, keyId } = serviceSelection;
      
      // Record the request attempt
      rateLimitManager.recordRequest(service, keyId, false, 0); // Pre-record as potential failure
      
      // Get the actual API key value
      const serviceConfig = this.serviceConfigs[service];
      const apiKey = await apiKeyStore.getServiceKeyWithFallback(service, serviceConfig.keyType);
      
      if (!apiKey) {
        request.callback({
          success: false,
          error: `No API key available for service ${service}`,
          service,
          keyId,
          responseTimeMs: Date.now() - startTime
        });
        return;
      }

      // Execute the request based on service type
      const result = await this.executeServiceRequest(service, apiKey, request);
      const responseTime = Date.now() - startTime;
      
      // Record successful request
      rateLimitManager.recordRequest(service, keyId, result.success, responseTime);
      
      request.callback({
        ...result,
        service,
        keyId,
        responseTimeMs: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(`Request ${request.id} failed:`, error);
      
      request.callback({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        service: 'igdb', // Default fallback
        keyId: 'error',
        responseTimeMs: responseTime
      });
    }
  }

  /**
   * Execute request for specific service
   */
  private async executeServiceRequest(
    service: ArtworkService,
    apiKey: string,
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    // This is a simplified implementation - in practice, you would
    // integrate with the actual service APIs here
    
    switch (service) {
      case 'igdb':
        return this.executeIGDBRequest(apiKey, request);
      case 'steamgriddb':
        return this.executeSteamGridDBRequest(apiKey, request);
      case 'thegamesdb':
        return this.executeTheGamesDBRequest(apiKey, request);
      case 'serpapi':
        return this.executeSerpAPIRequest(apiKey, request);
      case 'openai':
        return this.executeOpenAIRequest(apiKey, request);
      case 'deepseek':
        return this.executeDeepSeekRequest(apiKey, request);
      case 'gemini':
        return this.executeGeminiRequest(apiKey, request);
      default:
        return {
          success: false,
          error: `Unsupported service: ${service}`
        };
    }
  }

  /**
   * Service-specific request implementations
   */
  private async executeIGDBRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    // Implementation would integrate with existing IGDB API service
    console.log(`🎮 Executing IGDB request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      data: { source: 'igdb', type: request.type, mockData: true }
    };
  }

  private async executeSteamGridDBRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`🎨 Executing SteamGridDB request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return {
      success: true,
      data: { source: 'steamgriddb', type: request.type, mockData: true }
    };
  }

  private async executeTheGamesDBRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`🎮 Executing TheGamesDB request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 400));
    
    return {
      success: true,
      data: { source: 'thegamesdb', type: request.type, mockData: true }
    };
  }

  private async executeSerpAPIRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`🌐 Executing SerpAPI request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    return {
      success: true,
      data: { source: 'serpapi', type: request.type, mockData: true }
    };
  }

  private async executeOpenAIRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`🤖 Executing OpenAI request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1200));
    
    return {
      success: true,
      data: { source: 'openai', type: request.type, mockData: true }
    };
  }

  private async executeDeepSeekRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`🧠 Executing DeepSeek request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      data: { source: 'deepseek', type: request.type, mockData: true }
    };
  }

  private async executeGeminiRequest(
    apiKey: string, 
    request: ArtworkRequest
  ): Promise<{ success: boolean; data?: unknown; error?: string }> {
    console.log(`💎 Executing Gemini request ${request.type} with key ${apiKey.substring(0, 8)}...`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 900));
    
    return {
      success: true,
      data: { source: 'gemini', type: request.type, mockData: true }
    };
  }

  /**
   * Queue management
   */
  private addToQueue(request: ArtworkRequest): void {
    this.requestQueue.push(request);
    this.requestQueue.sort(this.compareRequestPriority);
    console.log(`📋 Added request ${request.id} to queue (${this.requestQueue.length} total)`);
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.requestQueue.length === 0) {
      return;
    }

    this.processing = true;

    try {
      while (this.requestQueue.length > 0) {
        const request = this.requestQueue.shift();
        if (request) {
          this.activeRequests.set(request.id, request);
          await this.processRequest(request);
          this.activeRequests.delete(request.id);
        }
      }
    } finally {
      this.processing = false;
    }
  }

  private compareRequestPriority(a: ArtworkRequest, b: ArtworkRequest): number {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  }

  private determinePriority(type: ArtworkRequestType): RequestPriority {
    switch (type) {
      case 'cover':
        return 'high';
      case 'search':
      case 'artwork':
        return 'medium';
      case 'screenshot':
      case 'bulk':
        return 'low';
      default:
        return 'medium';
    }
  }

  /**
   * Get service statistics
   */
  getServiceStatistics(): Record<ArtworkService, {
    totalRequests: number;
    successfulRequests: number;
    averageResponseTime: number;
    availableKeys: number;
    isCurrentlyLimited: boolean;
  }> {
    const stats = {} as Record<ArtworkService, {
      totalRequests: number;
      successfulRequests: number;
      averageResponseTime: number;
      availableKeys: number;
      isCurrentlyLimited: boolean;
    }>;

    const allUsage = rateLimitManager.getAllUsageStats();
    
    // Initialize stats for all services
    for (const service of Object.keys(this.serviceConfigs) as ArtworkService[]) {
      stats[service] = {
        totalRequests: 0,
        successfulRequests: 0,
        averageResponseTime: 0,
        availableKeys: 0,
        isCurrentlyLimited: false
      };
    }

    // Aggregate usage data
    for (const usage of allUsage) {
      if (stats[usage.service]) {
        stats[usage.service].totalRequests += usage.totalRequests;
        stats[usage.service].successfulRequests += usage.successfulRequests;
        stats[usage.service].averageResponseTime = usage.averageResponseTime;
      }
    }

    return stats;
  }

  /**
   * Get current queue status
   */
  getQueueStatus(): {
    queueLength: number;
    activeRequests: number;
    processing: boolean;
  } {
    return {
      queueLength: this.requestQueue.length,
      activeRequests: this.activeRequests.size,
      processing: this.processing
    };
  }
}

// Export singleton instance
export const artworkAPIRouter = new ArtworkAPIRouter();