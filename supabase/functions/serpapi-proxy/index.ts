// Deno global type declarations
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
  serve(handler: (req: Request) => Promise<Response> | Response): void;
};

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, client-id, content-type, apikey, x-client-info",
  "Access-Control-Allow-Methods": "POST, OPTIONS", // Changed to POST to match expected method
};

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { query } = await req.json();
    console.log("Received request for SerpAPI query:", query?.substring(0, 100) + "...");

    if (!query) {
      console.error("Query is missing from the request body.");
      return new Response(JSON.stringify({ error: "Query missing from request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const serpApiKey = Deno.env.get("SERPAPI_KEY");

    if (!serpApiKey) {
      console.error("SERPAPI_KEY not configured in environment variables.");
      return new Response(JSON.stringify({ error: "SerpAPI key not configured" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    console.log("Making request to SerpAPI...");
    const serpApiUrl = `https://serpapi.com/search?q=${encodeURIComponent(query)}&tbm=isch&api_key=${serpApiKey}`;
    
    const serpResponse = await fetch(serpApiUrl);

    console.log("SerpAPI Response status:", serpResponse.status);
    
    if (!serpResponse.ok) {
      const errorText = await serpResponse.text();
      console.error("SerpAPI error:", serpResponse.status, errorText);
      throw new Error(`SerpAPI error: ${serpResponse.status} - ${errorText}`);
    }

    const responseText = await serpResponse.text();
    console.log("SerpAPI Response data length:", responseText.length);
    console.log("SerpAPI Response data sample:", responseText.substring(0, 200));

    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log("Successfully parsed JSON response from SerpAPI");
    } catch (parseError) {
      console.error("Failed to parse SerpAPI response as JSON:", parseError);
      console.error("Raw response:", responseText);
      throw new Error(`Invalid JSON response from SerpAPI: ${responseText.substring(0, 500)}`);
    }

    return new Response(JSON.stringify(jsonData), {
      status: serpResponse.status,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Edge function error:", error);
    return new Response(JSON.stringify({
      error: error.message,
      details: "Failed to fetch data from SerpAPI"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});