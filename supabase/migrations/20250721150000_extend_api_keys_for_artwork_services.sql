-- Extend user_api_keys table to support artwork services and usage tracking
-- This migration adds support for multiple API services and usage analytics

-- First, add new columns to user_api_keys table
ALTER TABLE user_api_keys
ADD COLUMN IF NOT EXISTS service TEXT,
ADD COLUMN IF NOT EXISTS label TEXT,
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_used TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS success_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS average_response_time_ms INTEGER DEFAULT 0;

-- Create index for service-based queries
CREATE INDEX IF NOT EXISTS idx_user_api_keys_service ON user_api_keys(user_id, service);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_platform ON user_api_keys(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_user_api_keys_usage ON user_api_keys(user_id, last_used DESC);

-- Create RLS policies for service-based keys
CREATE POLICY "Users can manage their artwork service API keys" ON user_api_keys
    FOR ALL USING (auth.uid() = user_id);

-- Create a function to update API key usage statistics
CREATE OR REPLACE FUNCTION update_api_key_usage(
    p_user_id UUID,
    p_service TEXT,
    p_key_name TEXT,
    p_success BOOLEAN DEFAULT true,
    p_response_time_ms INTEGER DEFAULT 0
) RETURNS VOID AS $$
BEGIN
    UPDATE user_api_keys
    SET 
        usage_count = COALESCE(usage_count, 0) + 1,
        last_used = NOW(),
        success_count = CASE 
            WHEN p_success THEN COALESCE(success_count, 0) + 1 
            ELSE COALESCE(success_count, 0) 
        END,
        error_count = CASE 
            WHEN NOT p_success THEN COALESCE(error_count, 0) + 1 
            ELSE COALESCE(error_count, 0) 
        END,
        average_response_time_ms = CASE 
            WHEN p_response_time_ms > 0 THEN 
                (COALESCE(average_response_time_ms, 0) + p_response_time_ms) / 2
            ELSE COALESCE(average_response_time_ms, 0)
        END
    WHERE user_id = p_user_id 
    AND service = p_service 
    AND key_name = p_key_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_api_key_usage(UUID, TEXT, TEXT, BOOLEAN, INTEGER) TO authenticated;

-- Create a view for API key statistics
CREATE OR REPLACE VIEW user_api_key_stats AS
SELECT 
    user_id,
    service,
    platform,
    key_name,
    COUNT(*) as total_keys,
    SUM(usage_count) as total_requests,
    SUM(success_count) as total_successes,
    SUM(error_count) as total_errors,
    AVG(average_response_time_ms) as avg_response_time,
    MAX(last_used) as last_used,
    ROUND(
        CASE 
            WHEN SUM(usage_count) > 0 THEN 
                (SUM(success_count)::FLOAT / SUM(usage_count)::FLOAT) * 100 
            ELSE 0 
        END, 2
    ) as success_rate_percent
FROM user_api_keys
WHERE usage_count > 0
GROUP BY user_id, service, platform, key_name;

-- Grant access to the view
GRANT SELECT ON user_api_key_stats TO authenticated;

-- Create RLS policy for the stats view
CREATE POLICY "Users can view their own API key stats" ON user_api_key_stats
    FOR SELECT USING (auth.uid() = user_id);

-- Create a function to clean up old API usage data
CREATE OR REPLACE FUNCTION cleanup_old_api_usage() RETURNS VOID AS $$
BEGIN
    -- Reset usage statistics for keys not used in the last 90 days
    UPDATE user_api_keys 
    SET 
        usage_count = 0,
        success_count = 0,
        error_count = 0,
        average_response_time_ms = 0
    WHERE last_used < NOW() - INTERVAL '90 days'
    AND last_used IS NOT NULL;
    
    -- Log cleanup
    RAISE NOTICE 'Cleaned up old API usage data for keys not used in 90 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get available services and their key requirements
CREATE OR REPLACE FUNCTION get_artwork_service_requirements() 
RETURNS TABLE(
    service TEXT,
    display_name TEXT,
    key_type TEXT,
    key_label TEXT,
    is_required BOOLEAN,
    cost_per_request NUMERIC,
    quality_rating INTEGER,
    supported_features TEXT[]
) AS $$
BEGIN
    RETURN QUERY VALUES
        ('igdb', 'Internet Game Database', 'client_id', 'Client ID', true, 0.0, 5, ARRAY['search', 'cover', 'artwork', 'screenshots']),
        ('igdb', 'Internet Game Database', 'client_secret', 'Client Secret', true, 0.0, 5, ARRAY['search', 'cover', 'artwork', 'screenshots']),
        ('steamgriddb', 'SteamGridDB', 'api_key', 'API Key', true, 0.0, 5, ARRAY['cover', 'artwork']),
        ('thegamesdb', 'TheGamesDB', 'api_key', 'API Key', false, 0.0, 4, ARRAY['search', 'cover', 'artwork']),
        ('serpapi', 'SerpAPI', 'api_key', 'API Key', false, 0.01, 3, ARRAY['search', 'cover', 'bulk']),
        ('openai', 'OpenAI', 'api_key', 'API Key', false, 0.02, 4, ARRAY['search']),
        ('deepseek', 'DeepSeek', 'api_key', 'API Key', false, 0.005, 3, ARRAY['search']),
        ('gemini', 'Google Gemini', 'api_key', 'API Key', false, 0.01, 4, ARRAY['search']);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_artwork_service_requirements() TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE user_api_keys IS 'Stores encrypted API keys for both gaming platforms and artwork services';
COMMENT ON COLUMN user_api_keys.service IS 'Artwork service name (igdb, steamgriddb, serpapi, etc.)';
COMMENT ON COLUMN user_api_keys.platform IS 'Gaming platform name (steam, epic, etc.)';
COMMENT ON COLUMN user_api_keys.label IS 'User-friendly label for the API key';
COMMENT ON COLUMN user_api_keys.usage_count IS 'Total number of API requests made with this key';
COMMENT ON COLUMN user_api_keys.success_count IS 'Number of successful API requests';
COMMENT ON COLUMN user_api_keys.error_count IS 'Number of failed API requests';
COMMENT ON COLUMN user_api_keys.average_response_time_ms IS 'Average response time in milliseconds';

COMMENT ON FUNCTION update_api_key_usage IS 'Updates usage statistics for an API key after each request';
COMMENT ON FUNCTION cleanup_old_api_usage IS 'Cleans up old API usage statistics (run periodically)';
COMMENT ON FUNCTION get_artwork_service_requirements IS 'Returns available artwork services and their API key requirements';